import os
import requests
import json
import logging
import argparse
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
def log_product_proxies(product_name, existing_product):
    """Log associated proxies for the product."""
    if existing_product:
        existing_proxies = {config["apiSource"] for config in existing_product.get("operationGroup", {}).get("operationConfigs", [])}
        return f"{product_name}: {', '.join(existing_proxies) if existing_proxies else 'None'}"
    return f"{product_name}: No existing proxies."

def api_request(method, url, json_data=None, access_token=None):
    """Handles API requests with error handling."""
    headers = {"Authorization": f"Bearer {access_token}"}
    if json_data:
        headers["Content-Type"] = "application/json"
    
    try:
        response = requests.request(method, url, headers=headers, json=json_data)
        if response.status_code in [200, 201, 204]:
            return response
        elif response.status_code == 404:
            return None  # Indicate that the product is missing
        else:
            response.raise_for_status()
    except requests.exceptions.HTTPError as e:
        logging.error(f"HTTP error during API request: {e.response.text}")
        return None

def list_existing_products(apigee_org, access_token, products_to_check):
    """Fetch details only for products that exist in the input JSON file."""
    logging.info(f"🔍 Checking existing API products in {apigee_org}...")

    existing_products = {}

    for product_name in products_to_check:
        url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/apiproducts/{product_name}"
        response = api_request("GET", url, access_token=access_token)

        if response:
            try:
                response_json = response.json()
                if isinstance(response_json, dict):  # Ensure valid JSON
                    existing_products[product_name] = response_json
                    logging.info(f"✅ Product '{product_name}' exists.")
                else:
                    logging.warning(f"⚠️ Unexpected JSON response format for product '{product_name}'. Skipping.")
            except json.JSONDecodeError:
                logging.error(f"❌ Failed to parse API response for product '{product_name}'. Skipping.")

    return existing_products

def create_or_update_product(product_name, proxies, apigee_org, access_token, environment, existing_product, created_products_file, updated_products_file, no_update_products_file):
    """Creates a new API product if it doesn't exist, otherwise updates it."""

    # Scenario 1: Product Creation
    if not existing_product:
        logging.info(f"🆕 Creating product '{product_name}' in '{environment}'...")
        proxy_log = ', '.join(proxies) if proxies else 'No proxies associated.'
        with open(created_products_file, "a") as f:
            f.write(f"{product_name}:{environment}\n")
            f.write(f"    Proxies: {proxy_log}\n")

        url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/apiproducts"
        response = api_request("POST", url, {
            "name": product_name,
            "displayName": product_name,
            "environments": [environment],
            "approvalType": "auto",
            "attributes": [{"name": "access", "value": "public"}],
            "operationGroup": {
                "operationConfigType": "proxy",
                "operationConfigs": [{"apiSource": proxy, "operations": [{"resource": "/"}]} for proxy in proxies]
            }
        }, access_token)

        if response:
            logging.info(f"✅ Product '{product_name}' created successfully.")
        return "created"

    existing_proxies = {
        config["apiSource"]
        for config in existing_product.get("operationGroup", {}).get("operationConfigs", [])
    }

    updated_environments = list(set(existing_product.get("environments", []) + [environment.lower()]))
    new_proxies = [proxy for proxy in proxies if proxy not in existing_proxies]

    # Scenario 2: Nothing to update
    if set(updated_environments) == set(existing_product.get("environments", [])) and not new_proxies:
        logging.info(f"✅ Nothing to update for product '{product_name}' in '{environment}'.")
        with open(no_update_products_file, "a") as f:
            f.write(f"{product_name}:{environment}\n")
            f.write(f"    Proxies: {', '.join(existing_proxies) if existing_proxies else 'None'}\n")
        return "no_update"

    # Scenario 3: Product Update
    logging.info(f"🔄 Updating product '{product_name}' in '{environment}'...")
    proxy_log = ', '.join(new_proxies) if new_proxies else 'No new proxies.'
    with open(updated_products_file, "a") as f:
        f.write(f"{product_name}:{environment}\n")
        f.write(f"    New Proxies: {proxy_log}\n")
        f.write(f"    Existing Proxies: {', '.join(existing_proxies) if existing_proxies else 'None'}\n")

    # Combine existing and new proxies
    all_proxies = list(existing_proxies) + new_proxies

    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/apiproducts/{product_name}"
    response = api_request("PUT", url, {
        "name": product_name,
        "displayName": product_name,
        "approvalType": "auto",
        "environments": updated_environments,
        "attributes": [{"name": "access", "value": "public"}],
        "operationGroup": {
            "operationConfigType": "proxy",
            "operationConfigs": [{"apiSource": proxy, "operations": [{"resource": "/"}]} for proxy in all_proxies]
        }
    }, access_token)

    if response:
        logging.info(f"✅ Product '{product_name}' updated successfully.")
    return "updated"

def main(input_json_file, apigee_org, access_token, environment, created_products_file, updated_products_file,no_update_products_file):
    """Main function to handle API product deployment."""
    logging.info(f"🚀 Starting API product deployment for '{environment}' environment...")

    try:
        with open(input_json_file, "r") as file:
            products_data = json.load(file)

        grouped_products = defaultdict(list)
        for entry in products_data:
            product_name = entry["Product Name"]
            grouped_products[product_name].extend(entry["Proxies"])

        # Extract only product names from JSON
        products_to_check = list(grouped_products.keys())

        # Fetch existing products
        existing_products = list_existing_products(apigee_org, access_token, products_to_check)

        # Clear log files before writing
        open(created_products_file, "w").close()
        open(updated_products_file, "w").close()

        for product_name, proxies in grouped_products.items():
            existing_product = existing_products.get(product_name)

            if not existing_product:
                logging.info(f"🚀 Product '{product_name}' does not exist. Creating new in {environment}.")

            try:
                create_or_update_product(
                    product_name, 
                    proxies, 
                    apigee_org, 
                    access_token, 
                    environment, 
                    existing_product, 
                    created_products_file, 
                    updated_products_file,
                    no_update_products_file
                )
            except Exception as e:
                logging.error(f"⚠️ Failed to process product '{product_name}': {str(e)}")

        logging.info(f"✅ API product deployment for '{environment}' completed.")

    except Exception as e:
        logging.error(f"❌ An error occurred during product deployment: {str(e)}")
        exit(1)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Deploy API Products to Apigee.")
    parser.add_argument("--input_json_file", required=True, help="Path to the input JSON file.")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization name.")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API.")
    parser.add_argument("--environment", required=True, help="Apigee environment (dev, qa, stage, prod).")
    parser.add_argument("--created_products_file", required=True, help="File to log newly created products.")
    parser.add_argument("--updated_products_file", required=True, help="File to log updated products.")
    parser.add_argument("--no_update_products_file", required=True, help="File to log Non updated products.")

    args = parser.parse_args()
    main(args.input_json_file, args.apigee_org, args.access_token, args.environment, args.created_products_file, args.updated_products_file, args.no_update_products_file)
