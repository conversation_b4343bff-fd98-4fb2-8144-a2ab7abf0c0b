import os
import subprocess
import json
import logging
import argparse
from datetime import datetime

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def run_command(command):
    """Executes a shell command and returns the result."""
    logging.info(f"Running command: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        logging.error(f"Command failed: {result.stderr}")
    return result.stdout.strip()

def fetch_deployed_revisions(apigeex_org, environment, access_token, proxies_to_check):
    """Fetches deployed revisions only for proxies that exist in the given list (proxies_to_check)."""
    logging.info(f"Fetching deployed revisions for {environment}... Only checking proxies in our repository.")

    url = f"https://apigee.googleapis.com/v1/organizations/{apigeex_org}/environments/{environment}/deployments"
    command = f'curl -s -H "Authorization: Bearer {access_token}" "{url}"'
    response = run_command(command)

    deployed_revisions = {}

    try:
        deployments = json.loads(response)
        for proxy in deployments.get("deployments", []):
            proxy_name = proxy.get("apiProxy")
            revision = proxy.get("revision")

            if proxy_name in proxies_to_check:
                deployed_revisions[proxy_name] = revision
                logging.info(f"{proxy_name} is deployed in {environment} with revision {revision}")
            else:
                logging.debug(f"Ignoring {proxy_name} (not in repo)")

        return deployed_revisions
    except json.JSONDecodeError:
        logging.error("Failed to parse API response for deployed revisions.")
        return {}

def deploy_revision_to_qa(apigeex_org, access_token, proxy_name, revision, success_file, error_file):
    """Deploys the specified revision to QA and logs the status."""
    timestamp = datetime.utcnow().isoformat()
    url = f"https://apigee.googleapis.com/v1/organizations/{apigeex_org}/environments/qa/apis/{proxy_name}/revisions/{revision}/deployments?override=true"
    command = f'curl -s -X POST "{url}" -H "Authorization: Bearer {access_token}" -H "Content-Type: application/json"'
    result = run_command(command)

    if 'error' in result.lower():
        logging.error(f"Failed to deploy {proxy_name} revision {revision} to QA.")
        with open(error_file, "a") as f:
            f.write(f"{timestamp} | {proxy_name}:{revision} - Deployment Failed\n")
    else:
        logging.info(f"Successfully deployed {proxy_name} revision {revision} to QA.")
        with open(success_file, "a") as f:
            f.write(f"{timestamp} | {proxy_name}:{revision}\n")

def main(dev_revisions_file, apigeex_org, access_token):
    """Reads the latest DEV proxy revisions and deploys them to QA if necessary."""
    logging.info("Starting QA deployment validation...")

    success_file = "qa_success_deployments.txt"
    skipped_file = "qa_skipped_proxies.txt"
    error_file = "qa_deployment_errors.txt"
    previous_qa_revisions_file = "previous_qa_revisions.json"
    latest_qa_revisions_file = "latest_qa_revisions.json"
    qa_revisions_json = "qa_deployment_revisions.json"

    for file in [success_file, skipped_file, error_file, previous_qa_revisions_file, latest_qa_revisions_file]:
        open(file, "w").close()

    with open(dev_revisions_file, "r") as f:
        dev_revisions = json.load(f)

    logging.info(f"Found {len(dev_revisions)} proxies in DEV to check for QA deployment.")

    previous_qa_revisions = fetch_deployed_revisions(apigeex_org, "qa", access_token, dev_revisions.keys())
    with open(previous_qa_revisions_file, "w") as f:
        json.dump(previous_qa_revisions, f, indent=4)
    logging.info("Stored previous QA revisions before deployment.")

    for proxy_name, dev_revision in dev_revisions.items():
        qa_revision = previous_qa_revisions.get(proxy_name)

        if qa_revision is None:
            logging.info(f"{proxy_name} is not deployed in QA. Deploying revision {dev_revision}.")
            deploy_revision_to_qa(apigeex_org, access_token, proxy_name, dev_revision, success_file, error_file)
        elif int(dev_revision) > int(qa_revision):
            logging.info(f"{proxy_name} revision in DEV ({dev_revision}) is newer than QA ({qa_revision}). Updating QA.")
            deploy_revision_to_qa(apigeex_org, access_token, proxy_name, dev_revision, success_file, error_file)
        else:
            logging.info(f"{proxy_name} is already up to date in QA with revision {qa_revision}. Skipping.")
            with open(skipped_file, "a") as f:
                f.write(f"{proxy_name}:{qa_revision}\n")

    latest_qa_revisions = fetch_deployed_revisions(apigeex_org, "qa", access_token, dev_revisions.keys())
    with open(latest_qa_revisions_file, "w") as f:
        json.dump(latest_qa_revisions, f, indent=4)
    logging.info("Stored latest QA revisions after deployment.")

    qa_revisions = {
        proxy: {
            "previous_revision": previous_qa_revisions.get(proxy, "N/A"),
            "latest_revision": latest_qa_revisions.get(proxy, "N/A")
        }
        for proxy in dev_revisions.keys()
    }

    with open(qa_revisions_json, "w") as json_file:
        json.dump(qa_revisions, json_file, indent=4)
    logging.info(f"Stored QA deployment revisions in {qa_revisions_json}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Deploy proxies to QA based on DEV revisions.")
    parser.add_argument("--dev_revisions_file", required=True, help="File containing deployed DEV revisions.")
    parser.add_argument("--apigeex_org", required=True, help="Apigee organization name.")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API.")

    args = parser.parse_args()
    main(args.dev_revisions_file, args.apigeex_org, args.access_token)
