import os
import json
import argparse
import logging
import re
import subprocess

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def run_command(command):
    """Executes a shell command and returns the result."""
    logging.info(f"Running command: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        logging.error(f"Command failed: {result.stderr}")
    return result.stdout.strip()

def extract_revision(xml_content):
    """Extracts the revision number from an XML content."""
    match = re.search(r'revision="([^"]+)"', xml_content)
    return match.group(1) if match else None

def detect_proxies_from_artifacts(artifact_path):
    """Extracts proxies only from the artifact folder."""
    proxies = {}

    for proxy_folder in os.listdir(artifact_path):
        proxy_path = os.path.join(artifact_path, proxy_folder, "apiproxy")
        xml_file_path = os.path.join(proxy_path, f"{proxy_folder}.xml")

        if os.path.exists(xml_file_path):
            proxies[proxy_folder] = None  # Store proxy names, revision will be fetched later
            logging.info(f"✅ Proxy detected in artifact: {proxy_folder}")
        else:
            logging.warning(f"⚠️ Skipping {proxy_folder}, no XML definition found.")

    return proxies

def fetch_deployed_revisions(apigee_org, environment, access_token, proxy_list):
    """Fetch deployed revisions for only the proxies in the artifact folder."""
    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/deployments"
    command = f'curl -s -H "Authorization: Bearer {access_token}" "{url}"'
    response = run_command(command)

    deployed_revisions = {}

    try:
        deployments = json.loads(response)
        for dep in deployments.get("deployments", []):
            proxy_name = dep["apiProxy"]
            revision = dep["revision"]

            if proxy_name in proxy_list:  # Only keep proxies present in artifact folder
                deployed_revisions[proxy_name] = revision
                logging.info(f"🔍 {proxy_name} is deployed in {environment} with revision {revision}")

        return deployed_revisions
    except json.JSONDecodeError:
        logging.error("Failed to parse API response for deployed revisions.")
        return {}

def compare_revisions(artifact_revisions, deployed_revisions):
    """Compares artifact revisions with deployed revisions and categorizes them."""
    comparison = {}
    success_deployments = []
    skipped_proxies = []
    deployment_errors = []

    for proxy, local_revision in artifact_revisions.items():
        deployed_revision = deployed_revisions.get(proxy, "N/A")
        status = "✅ Up-to-date" if str(local_revision) == str(deployed_revision) else "🔄 Needs Deployment"
        comparison[proxy] = {
            "artifact_revision": local_revision,
            "deployed_revision": deployed_revision,
            "status": status
        }
        if status == "✅ Up-to-date":
            skipped_proxies.append(proxy)
        elif status == "🔄 Needs Deployment":
            success_deployments.append(proxy)
        else:
            deployment_errors.append(proxy)

    return comparison, success_deployments, skipped_proxies, deployment_errors

def save_comparison_results(results, output_file):
    """Save the comparison results to a JSON file."""
    with open(output_file, "w") as f:
        json.dump(results, f, indent=4)
    logging.info(f"📄 Stored revision comparison results in {output_file}")

def save_deployment_status(success_file, skipped_file, error_file, success_deployments, skipped_proxies, deployment_errors):
    """Save deployment status results."""
    with open(success_file, "w") as f:
        f.write("\n".join(success_deployments))
    with open(skipped_file, "w") as f:
        f.write("\n".join(skipped_proxies))
    with open(error_file, "w") as f:
        f.write("\n".join(deployment_errors))
    logging.info("📄 Deployment status files created.")

def main(artifact_path, apigee_org, environment, access_token, output_file, dev_revisions_file, success_file, skipped_file, error_file):
    logging.info(f"🔍 Extracting proxies from artifact at {artifact_path}...")

    # **Fix:** Extract only proxies present in artifact folder
    artifact_proxies = detect_proxies_from_artifacts(artifact_path)

    logging.info(f"📡 Fetching deployed revisions from Apigee {environment} environment...")
    
    # **Fix:** Fetch deployed revisions only for the proxies present in artifacts
    deployed_revisions = fetch_deployed_revisions(apigee_org, environment, access_token, artifact_proxies.keys())

    logging.info("🔄 Comparing revisions...")
    comparison_results, success_deployments, skipped_proxies, deployment_errors = compare_revisions(artifact_proxies, deployed_revisions)

    save_comparison_results(comparison_results, output_file)
    save_deployment_status(success_file, skipped_file, error_file, success_deployments, skipped_proxies, deployment_errors)

    # **Fix:** Save only filtered deployed revisions for QA
    with open(dev_revisions_file, "w") as f:
        json.dump(deployed_revisions, f, indent=4)
    logging.info(f"📄 Stored deployed revisions for QA in {dev_revisions_file}")

    logging.info("✅ Revision extraction and comparison completed.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract and Compare Proxy Revisions from Artifacts vs Apigee Deployment")
    parser.add_argument("--artifact_path", required=True, help="Path to extracted artifacts containing proxies")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization name")
    parser.add_argument("--environment", required=True, help="Apigee environment (e.g., dev)")
    parser.add_argument("--access_token", required=True, help="Apigee access token")
    parser.add_argument("--output_file", required=True, help="File to store revision comparison results")
    parser.add_argument("--dev_revisions_file", required=True, help="File to store deployed revisions for QA deployment")
    parser.add_argument("--success_file", required=True, help="File to store successfully deployed proxies")
    parser.add_argument("--skipped_file", required=True, help="File to store skipped proxies")
    parser.add_argument("--error_file", required=True, help="File to store deployment errors")

    args = parser.parse_args()

    main(
        args.artifact_path, 
        args.apigee_org, 
        args.environment, 
        args.access_token, 
        args.output_file, 
        args.dev_revisions_file, 
        args.success_file, 
        args.skipped_file, 
        args.error_file
    )
