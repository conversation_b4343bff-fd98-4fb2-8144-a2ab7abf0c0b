import argparse
import os
import json
import requests
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def api_request(method, url, access_token, json_data=None):
    headers = {"Authorization": f"Bearer {access_token}"}
    if json_data:
        headers["Content-Type"] = "application/json"
    response = requests.request(method, url, headers=headers, json=json_data)
    if response.status_code in [200, 201, 204]:
        return response
    elif response.status_code == 404:
        return None
    else:
        logging.error(f"API call failed: {response.status_code} - {response.text}")
        return None

def get_app(org, developer_email, app_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}"
    response = api_request("GET", url, access_token)
    return response.json() if response else None

def create_app(org, developer_email, app_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps"
    payload = {
        "name": app_name,
        "apiProducts": [],
        "attributes": [{"name": "access", "value": "public"}],
        "status": "approved"
    }
    response = api_request("POST", url, access_token, payload)
    if response:
        logging.info(f"Created App '{app_name}'.")

def add_missing_products(org, developer_email, app_name, consumer_key, missing_products, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}/keys/{consumer_key}"
    payload = {
        "apiProducts": missing_products
    }
    response = api_request("POST", url, access_token, payload)
    if response:
        logging.info(f"Added missing products {missing_products} to App '{app_name}'.")

def main(input_json_file, apigee_org, developer_email, access_token):
    with open(input_json_file, "r") as f:
        data = json.load(f)

    for entry in data:
        app_name = entry["App Name"]
        required_product = entry["Product Name"]

        app = get_app(apigee_org, developer_email, app_name, access_token)

        if not app:
            logging.info(f"App '{app_name}' does not exist. Creating it...")
            create_app(apigee_org, developer_email, app_name, access_token)
            continue  # App created but no products yet

        credentials = app.get("credentials", [])
        if not credentials:
            logging.warning(f"App '{app_name}' has no credentials. Skipping.")
            continue

        consumer_key = credentials[0]["consumerKey"]
        current_products = [p["apiproduct"] for p in credentials[0].get("apiProducts", [])]

        if required_product in current_products:
            logging.info(f"Product '{required_product}' already exists for App '{app_name}'. Skipping.")
        else:
            logging.info(f"Product '{required_product}' missing for App '{app_name}'. Adding it...")
            add_missing_products(apigee_org, developer_email, app_name, consumer_key, [required_product], access_token)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Validate and fix Apigee Apps and their Product Mappings")
    parser.add_argument("--input_json_file", required=True, help="Path to temp_products.json")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization ID")
    parser.add_argument("--developer_email", required=True, help="Developer email for the apps")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API")
    args = parser.parse_args()

    main(args.input_json_file, args.apigee_org, args.developer_email, args.access_token)
