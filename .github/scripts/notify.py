import argparse
import os
import requests

# Helper function to read file content
def read_file_content(file_path, default_msg):
    try:
        with open(file_path, "r") as file:
            content = file.read().strip()
            return content if content else default_msg
    except FileNotFoundError:
        return default_msg

# Function to send email via SendGrid
def send_email(status, environment, actor, pipeline_url, recipients, success_file, skipped_file, error_file, created_products_file, updated_products_file):
    sendgrid_api_key = os.getenv("SENDGRID_API_KEY")
    if not sendgrid_api_key:
        print("Error: SENDGRID_API_KEY not found in environment variables.")
        exit(1)

    # Unique recipients
    unique_recipients = list(set(email.strip() for email in recipients.split(",") if email.strip()))
    print(f"Unique Recipients: {unique_recipients}")

    # Read proxy-related files
    success_deployments = list(set(read_file_content(success_file, "").splitlines()))
    success_html = "".join(f"<li>{line}</li>" for line in success_deployments)
    skipped_deployments = read_file_content(skipped_file, "No proxies skipped.")
    error_deployments = read_file_content(error_file, "No deployment errors detected.")

    # Read product-related files
    created_products = read_file_content(created_products_file, "No new API products created.")
    updated_products = read_file_content(updated_products_file, "No API products updated.")

    # Prepare HTML content
    html_content = f"""
    <html>
      <body style='font-family: Arial, sans-serif;'>
        <p>Hi Team,</p>
        <h2 style='color: green;'>Apigee Deployment Summary - {environment.upper()}</h2>
        <p><strong>Status:</strong> {status.upper()}</p>
        <p><strong>Triggered By:</strong> {actor}</p>
        <p><strong>Pipeline URL:</strong> <a href='{pipeline_url}'>{pipeline_url}</a></p>
        <h3 style='color: blue;'>✅ Successfully Deployed Proxies:</h3>
        <pre>{success_deployments}</pre>
        <h3 style='color: orange;'>⏭️ Skipped Proxies (Already Up-to-Date):</h3>
        <pre>{skipped_deployments}</pre>
        <h3 style='color: red;'>❌ Deployment Errors:</h3>
        <pre>{error_deployments}</pre>
        <h3 style='color: green;'>📦 New API Products Created:</h3>
        <pre>{created_products}</pre>
        <h3 style='color: blue;'>🔄 Updated API Products:</h3>
        <pre>{updated_products}</pre>
        <br>
        <p>Thank you,</p>
        <p><strong>Apigee CI/CD Automation</strong></p>
      </body>
    </html>
    """

    # SendGrid API payload
    payload = {
        "personalizations": [{"to": [{"email": email} for email in unique_recipients]}],
        "from": {"email": "<EMAIL>"},
        "subject": f"Apigee CI/CD - {environment.upper()} ({status.upper()})",
        "content": [{"type": "text/html", "value": html_content}]
    }

    # Send email request
    response = requests.post(
        "https://api.sendgrid.com/v3/mail/send",
        headers={
            "Authorization": f"Bearer {sendgrid_api_key}",
            "Content-Type": "application/json"
        },
        json=payload
    )

    if response.status_code == 202:
        print(f"✅ Email notification sent successfully to: {', '.join(unique_recipients)}")
    else:
        print(f"❌ Failed to send email. Status Code: {response.status_code}, Response: {response.text}")
        exit(1)

# Main execution
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Send Apigee Deployment Notification")
    parser.add_argument("--status", required=True, help="Deployment status: success or failure")
    parser.add_argument("--environment", required=True, help="Target environment (e.g., dev, qa, stage, prod)")
    parser.add_argument("--actor", required=True, help="User who triggered the workflow")
    parser.add_argument("--pipeline_url", required=True, help="GitHub Actions pipeline run URL")
    parser.add_argument("--recipients", required=True, help="Comma-separated list of email recipients")
    parser.add_argument("--success_file", required=False, help="File listing successful deployments")
    parser.add_argument("--skipped_file", required=False, help="File listing skipped deployments")
    parser.add_argument("--error_file", required=False, help="File listing deployment errors")
    parser.add_argument("--created_products_file", required=False, help="File listing created products")
    parser.add_argument("--updated_products_file", required=False, help="File listing updated products")

    args = parser.parse_args()

    send_email(
        args.status,
        args.environment,
        args.actor,
        args.pipeline_url,
        args.recipients,
        args.success_file,
        args.skipped_file,
        args.error_file,
        args.created_products_file,
        args.updated_products_file
    )
