#!/bin/bash

# Input parameters
TEMP_JSON_FILE=$1
ENVIRONMENT=$2
APIGEE_ORG=$3
ACCESS_TOKEN=$4

# Exit if any command fails
set -e

# Define dynamic log file names based on the environment
CREATED_PRODUCTS_FILE="${ENVIRONMENT,,}_created_products.txt"
UPDATED_PRODUCTS_FILE="${ENVIRONMENT,,}_updated_products.txt"
NO_UPDATE_PRODUCTS_FILE="${ENVIRONMENT,,}_no_update_products.txt"

# Clear previous logs
> "$CREATED_PRODUCTS_FILE"
> "$UPDATED_PRODUCTS_FILE"
> "$NO_UPDATE_PRODUCTS_FILE"

# Deploy products using the Python script
echo " Deploying products for environment: $ENVIRONMENT"
python3 central-repo/.github/scripts/deploy-products.py \
    --input_json_file "$TEMP_JSON_FILE" \
    --apigee_org "$APIGEE_ORG" \
    --access_token "$ACCESS_TOKEN" \
    --environment "$ENVIRONMENT" \
    --created_products_file "$CREATED_PRODUCTS_FILE" \
    --updated_products_file "$UPDATED_PRODUCTS_FILE" \
    --no_update_products_file "$NO_UPDATE_PRODUCTS_FILE"

# Log results
echo " Product deployment completed for $ENVIRONMENT."
echo " New Products Created:"
cat "$CREATED_PRODUCTS_FILE" || echo "No new products created."

echo " Updated Products:"
cat "$UPDATED_PRODUCTS_FILE" || echo "No products updated."

echo " Products With No Changes:"
cat "$NO_UPDATE_PRODUCTS_FILE" || echo "All products required updates."
