import argparse
import os
import json
import requests
import logging

# Logging config
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def api_request(method, url, access_token, json_data=None):
    headers = {"Authorization": f"Bearer {access_token}"}
    if json_data:
        headers["Content-Type"] = "application/json"

    response = requests.request(method, url, headers=headers, json=json_data)
    if response.status_code in [200, 201, 204]:
        return response
    elif response.status_code == 404:
        return None
    else:
        logging.error(f"API call failed: {response.status_code} - {response.text}")
        return None

def get_product_details(org, product_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/apiproducts/{product_name}"
    response = api_request("GET", url, access_token)
    return response.json() if response else None

def check_proxy_exists(org, environment, proxy_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/environments/{environment}/apis/{proxy_name}/deployments"
    response = api_request("GET", url, access_token)
    return response is not None

def update_product(org, product_name, product_data, proxies, access_token):
    existing_proxies = {
        config["apiSource"]
        for config in product_data.get("operationGroup", {}).get("operationConfigs", [])
    }

    new_configs = list(product_data["operationGroup"]["operationConfigs"])
    added = False
    for proxy in proxies:
        if proxy not in existing_proxies:
            logging.info(f"Adding proxy '{proxy}' to product '{product_name}'")
            new_configs.append({"apiSource": proxy, "operations": [{"resource": "/"}]})
            added = True

    if not added:
        logging.info(f"✅ Product '{product_name}' already has all proxies. No update needed.")
        return

    payload = {
        "name": product_name,
        "displayName": product_name,
        "environments": list(set(product_data.get("environments", []) + [args.environment])),
        "approvalType": product_data.get("approvalType", "auto"),
        "attributes": product_data.get("attributes", [{"name": "access", "value": "public"}]),
        "operationGroup": {
            "operationConfigType": "proxy",
            "operationConfigs": new_configs
        }
    }

    url = f"https://apigee.googleapis.com/v1/organizations/{org}/apiproducts/{product_name}"
    response = api_request("PUT", url, access_token, json_data=payload)
    if response:
        logging.info(f"✅ Product '{product_name}' updated with missing proxies.")
        
def create_product(org, product_name, proxies, environment, access_token):
    payload = {
        "name": product_name,
        "displayName": product_name,
        "environments": [environment],
        "approvalType": "auto",
        "attributes": [{"name": "access", "value": "public"}],
        "operationGroup": {
            "operationConfigType": "proxy",
            "operationConfigs": [{"apiSource": proxy, "operations": [{"resource": "/"}]} for proxy in proxies]
        }
    }

    url = f"https://apigee.googleapis.com/v1/organizations/{org}/apiproducts"
    response = api_request("POST", url, access_token, json_data=payload)
    if response:
        logging.info(f"🆕 Product '{product_name}' created successfully with proxies.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Validate and fix Apigee Product Proxy Mappings")
    parser.add_argument("--input_json_file", required=True, help="Path to temp_products.json")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization ID")
    parser.add_argument("--environment", required=True, help="Apigee environment (dev, qa, stage, prod)")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API")
    args = parser.parse_args()

    try:
        with open(args.input_json_file, "r") as f:
            data = json.load(f)

        for entry in data:
            product_name = entry["Product Name"]
            proxies = entry["Proxies"]
            valid_proxies = []

            for proxy in proxies:
                if check_proxy_exists(args.apigee_org, args.environment, proxy, args.access_token):
                    valid_proxies.append(proxy)
                else:
                    logging.warning(f"❌ Proxy '{proxy}' not found in {args.environment}. Skipping update.")

            if not valid_proxies:
                continue

            product_data = get_product_details(args.apigee_org, product_name, args.access_token)
            if not product_data:
                logging.warning(f"⚠️ Product '{product_name}' not found. Attempting to create it...")
                create_product(args.apigee_org, product_name, valid_proxies, args.environment, args.access_token)
            else:
                update_product(args.apigee_org, product_name, product_data, valid_proxies, args.access_token)

    except Exception as e:
        logging.error(f"Exception occurred: {str(e)}")
        exit(1)
