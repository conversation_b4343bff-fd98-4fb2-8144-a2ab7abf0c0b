import argparse
import pandas as pd
import json
import os

def convert_excel_to_json(excel_file, output_file):
    if not os.path.exists(excel_file):
        raise FileNotFoundError(f"❌ Excel file not found: {excel_file}")

    df = pd.read_excel(excel_file)

    required_columns = {"App Name", "Product Name", "Proxies"}
    if not required_columns.issubset(df.columns):
        raise ValueError(f"❌ Excel file must contain columns: {required_columns}")

    output_data = []

    for _, row in df.iterrows():
        app_name = str(row["App Name"]).strip()
        product_name = str(row["Product Name"]).strip()
        raw_proxies = str(row["Proxies"])

        # Split proxies by newlines and clean whitespace
        proxies = [line.strip() for line in raw_proxies.splitlines() if line.strip()]

        if proxies:
            output_data.append({
                "App Name": app_name,
                "Product Name": product_name,
                "Proxies": proxies
            })

    # Ensure output directory exists
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    with open(output_file, "w") as f:
        json.dump(output_data, f, indent=2)

    print(f"✅ JSON mapping file written to: {output_file}")
    print(json.dumps(output_data, indent=2))  # Optional: print to console

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert Excel product mappings to JSON")
    parser.add_argument("--excel_file", required=True, help="Path to the Excel file")
    parser.add_argument("--output_file", required=True, help="Path to output JSON file")

    args = parser.parse_args()
    convert_excel_to_json(args.excel_file, args.output_file)
