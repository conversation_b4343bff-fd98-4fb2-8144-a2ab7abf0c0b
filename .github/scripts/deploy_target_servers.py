import os
import json
import requests
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def get_existing_target_servers(apigee_org, environment, access_token):
    """Fetch existing target servers in the specified environment."""
    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/targetservers"
    headers = {"Authorization": f"Bearer {access_token}"}

    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        logging.warning(f"Failed to fetch existing target servers. Status: {response.status_code}, Response: {response.text}")
        return []

def deploy_target_server(apigee_org, environment, access_token, config_path):
    """Deploy a single target server from the given config file."""
    with open(config_path, "r") as config_file:
        target_server_config = json.load(config_file)

    target_server_name = target_server_config.get("name")
    if not target_server_name:
        logging.error(f"Missing 'name' field in config: {config_path}")
        return False

    # Check if the target server already exists
    existing_servers = get_existing_target_servers(apigee_org, environment, access_token)
    if target_server_name in existing_servers:
        logging.info(f"✅ Target server '{target_server_name}' already exists. Updating...")
        url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/targetservers/{target_server_name}"
        method = "PUT"
    else:
        logging.info(f"🚀 Creating new target server '{target_server_name}'...")
        url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/targetservers"
        method = "POST"

    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    response = requests.request(method, url, headers=headers, json=target_server_config)

    if response.status_code in [200, 201]:
        logging.info(f"✅ Successfully deployed target server '{target_server_name}'.")
        return True
    else:
        logging.error(f"❌ Failed to deploy target server '{target_server_name}'. Status: {response.status_code}, Response: {response.text}")
        return False

def main(config_dir, apigee_org, environment, access_token):
    """Main function to deploy all target servers from the specified config directory."""
    success_log = "target_servers_success.log"
    failed_log = "target_servers_failed.log"

    # Clear previous logs
    open(success_log, "w").close()
    open(failed_log, "w").close()

    if not os.path.exists(config_dir):
        logging.error(f"Configuration directory '{config_dir}' not found.")
        exit(1)

    for config_file in os.listdir(config_dir):
        if config_file.endswith(".json"):
            config_path = os.path.join(config_dir, config_file)
            logging.info(f"📄 Processing config: {config_file}")

            if deploy_target_server(apigee_org, environment, access_token, config_path):
                with open(success_log, "a") as success_log_file:
                    success_log_file.write(f"{config_file} - Success\n")
            else:
                with open(failed_log, "a") as failed_log_file:
                    failed_log_file.write(f"{config_file} - Failed\n")

    logging.info("🚀 Target server deployment completed.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Deploy Target Servers to Apigee.")
    parser.add_argument("--config_dir", required=True, help="Directory containing target server JSON configs.")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization name.")
    parser.add_argument("--environment", required=True, help="Target environment (dev, qa, stage, prod).")
    parser.add_argument("--access_token", required=True, help="Apigee access token.")

    args = parser.parse_args()

    main(args.config_dir, args.apigee_org, args.environment, args.access_token)
