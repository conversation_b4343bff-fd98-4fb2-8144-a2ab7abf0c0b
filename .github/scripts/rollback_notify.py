import os
import argparse
import logging
import requests
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

SENDGRID_API_KEY = os.getenv("SENDGRID_API_KEY")

def send_email(status, environment, actor, run_url, recipients, rolled_back_proxies):
    """Send rollback notification via email using SendGrid API."""
    subject = f"Apigee Rollback {status.upper()} in {environment.upper()}"

    # Read rolled back proxies from the file
    try:
        with open(rolled_back_proxies, "r") as f:
            proxies_list = f.read().strip().splitlines()
    except FileNotFoundError:
        proxies_list = []

    # Create email body with rollback details
    email_body = f"""
    <html>
    <head>
    <style>
        body {{ font-family: Arial, sans-serif; }}
        .status {{ color: white; padding: 5px 10px; border-radius: 5px; display: inline-block; }}
        .success {{ background-color: #28a745; }}
        .failure {{ background-color: #dc3545; }}
        .content {{ margin: 20px; }}
        .footer {{ margin-top: 20px; font-size: 14px; color: #555; }}
    </style>
    </head>
    <body>
    <div class="content">
        <h2>Apigee Rollback Notification</h2>
        <p><strong>Status:</strong> <span class="status {status.lower()}">{status.upper()}</span></p>
        <p><strong>Environment:</strong> {environment}</p>
        <p><strong>Triggered By:</strong> {actor}</p>
        <p><strong>GitHub Action Run:</strong> <a href="{run_url}" style="color: #007bff; text-decoration: none;">View Details</a></p>
    """

    # Include rolled-back proxies if available
    if proxies_list:
        email_body += "<p><strong>✅ Rolled Back Proxies:</strong></p><ul>"
        for proxy in proxies_list:
            email_body += f"<li>{proxy}</li>"
        email_body += "</ul>"
    else:
        email_body += "<p>No proxies were rolled back.</p>"

    email_body += """
        <p class="footer">Thank you,<br><strong>Apigee CI/CD Automation</strong></p>
    </div>
    </body>
    </html>
    """

    email_data = {
        "personalizations": [
            {
                "to": [{"email": recipient.strip()} for recipient in recipients.split(",")],
                "subject": subject,
            }
        ],
        "from": {"email": "<EMAIL>", "name": "Apigee CI/CD"},
        "content": [{"type": "text/html", "value": email_body}],
    }

    # Send email using SendGrid API
    headers = {
        "Authorization": f"Bearer {SENDGRID_API_KEY}",
        "Content-Type": "application/json",
    }

    # Send email
    response = requests.post("https://api.sendgrid.com/v3/mail/send", headers=headers, json=email_data)

    if response.status_code == 202:
        logging.info(f"✅ Rollback notification sent successfully to {recipients}.")
    else:
        logging.error(f"❌ Failed to send email: {response.text}")

def main():
    """Parse arguments and send rollback notification."""
    parser = argparse.ArgumentParser(description="Send Apigee Rollback Notification")
    parser.add_argument("--status", required=True, help="Rollback status (success/failure)")
    parser.add_argument("--environment", required=True, help="Environment (qa/stage/prod)")
    parser.add_argument("--actor", required=True, help="Person who triggered the rollback")
    parser.add_argument("--run_url", required=True, help="GitHub Actions Run URL")
    parser.add_argument("--recipients", required=True, help="Comma-separated list of email recipients")
    parser.add_argument("--rolled_back_proxies", required=True, help="File containing successfully rolled back proxies")

    args = parser.parse_args()

    # Call the send_email function
    send_email(
        args.status,
        args.environment,
        args.actor,
        args.run_url,
        args.recipients,
        args.rolled_back_proxies,
    )

if __name__ == "__main__":
    main()
