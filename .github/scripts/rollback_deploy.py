import os
import json
import argparse
import requests
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def load_json(file_path):
    """Load JSON data from the specified file."""
    try:
        with open(file_path, "r") as file:
            return json.load(file)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logging.error(f"Failed to load JSON file {file_path}: {e}")
        exit(1)

def rollback_proxy(apigee_org, environment, access_token, proxy_name, previous_revision):
    """Rollback a proxy to its previous revision."""
    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/apis/{proxy_name}/revisions/{previous_revision}/deployments?override=true"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    logging.info(f"🔄 Rolling back {proxy_name} to revision {previous_revision} in {environment}...")

    response = requests.post(url, headers=headers)
    
    if response.status_code == 200 or response.status_code == 201:
        logging.info(f"✅ Successfully rolled back {proxy_name} to revision {previous_revision}.")
        return True
    else:
        logging.error(f"❌ Failed to rollback {proxy_name}. Response: {response.status_code}, {response.text}")
        return False

def main(deployment_file, apigee_org, environment, access_token, output_file):
    """Main function for rolling back proxies based on the deployment mapping JSON."""
    logging.info(f"🚀 Starting rollback for environment: {environment}")

    deployment_data = load_json(deployment_file)

    with open(output_file, "w") as summary_file:
        for proxy_name, revisions in deployment_data.items():
            previous_revision = revisions.get("previous_revision")

            if previous_revision and previous_revision != "N/A":
                success = rollback_proxy(apigee_org, environment, access_token, proxy_name, previous_revision)

                if success:
                    summary_file.write(f"{proxy_name}: Rolled back to revision {previous_revision}\n")
                else:
                    summary_file.write(f"{proxy_name}: Failed to rollback\n")
            else:
                logging.warning(f"⚠️ Skipping {proxy_name}: No valid previous revision found.")
                summary_file.write(f"{proxy_name}: No valid previous revision found\n")

    logging.info(f"🎯 Rollback completed. Summary saved to {output_file}.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Rollback Apigee proxies to previous revisions.")
    parser.add_argument("--deployment_file", required=True, help="Path to the deployment mapping JSON file.")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization name.")
    parser.add_argument("--environment", required=True, help="Environment for rollback (stage/prod).")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API.")
    parser.add_argument("--output_file", required=True, help="File to store rollback summary.")

    args = parser.parse_args()

    main(args.deployment_file, args.apigee_org, args.environment, args.access_token, args.output_file)
