import argparse
import json
import requests
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def api_request(method, url, access_token, json_data=None):
    headers = {"Authorization": f"Bearer {access_token}", "Content-Type": "application/json"}
    response = requests.request(method, url, headers=headers, json=json_data)
    if response.status_code in [200, 201, 204]:
        return response.json() if response.content else None
    else:
        logging.error(f"API call failed: {response.status_code} - {response.text}")
        return None

def get_app(org, developer_email, app_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}"
    return api_request("GET", url, access_token)

def create_key(org, developer_email, app_name, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}/keys/create"
    return api_request("POST", url, access_token, json_data={})

def add_products_to_key(org, developer_email, app_name, consumer_key, products, access_token):
    url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}/keys/{consumer_key}"
    payload = {"apiProducts": products}
    api_request("POST", url, access_token, json_data=payload)

def delete_old_keys(org, developer_email, app_name, current_key, access_token):
    app = get_app(org, developer_email, app_name, access_token)
    for cred in app.get("credentials", []):
        key = cred.get("consumerKey")
        if key != current_key:
            url = f"https://apigee.googleapis.com/v1/organizations/{org}/developers/{developer_email}/apps/{app_name}/keys/{key}"
            response = requests.delete(url, headers={"Authorization": f"Bearer {access_token}"})
            if response.status_code == 204:
                logging.info(f"Deleted old key: {key}")
            else:
                logging.warning(f"Failed to delete key {key}: {response.text}")

def main(input_json_file, apigee_org, developer_email, access_token, app_names, delete_old_keys_flag):
    with open(input_json_file, "r") as f:
        data = json.load(f)

    app_list = [name.strip() for name in app_names.split(",") if name.strip()]

    for app_name in app_list:
        matching_products = [row["Product Name"] for row in data if row["App Name"] == app_name]

        if not matching_products:
            logging.warning(f"No product mapping found for app: {app_name}")
            continue

        app = get_app(apigee_org, developer_email, app_name, access_token)
        if not app:
            logging.warning(f"App '{app_name}' does not exist. Skipping.")
            continue

        key_data = create_key(apigee_org, developer_email, app_name, access_token)
        if not key_data:
            logging.error(f"Failed to create credential for app: {app_name}")
            continue

        consumer_key = key_data["consumerKey"]
        add_products_to_key(apigee_org, developer_email, app_name, consumer_key, matching_products, access_token)
        logging.info(f"Added products to new key {consumer_key} for app {app_name}")

        if delete_old_keys_flag.lower() == "yes":
            delete_old_keys(apigee_org, developer_email, app_name, consumer_key, access_token)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create new credentials for apps and assign products")
    parser.add_argument("--input_json_file", required=True, help="Path to temp_products.json")
    parser.add_argument("--apigee_org", required=True, help="Apigee organization ID")
    parser.add_argument("--developer_email", required=True, help="Developer email")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API")
    parser.add_argument("--app_names", required=True, help="Comma-separated app names")
    parser.add_argument("--delete_old_keys", required=True, help="yes or no")
    args = parser.parse_args()

    main(args.input_json_file, args.apigee_org, args.developer_email, args.access_token, args.app_names, args.delete_old_keys)
