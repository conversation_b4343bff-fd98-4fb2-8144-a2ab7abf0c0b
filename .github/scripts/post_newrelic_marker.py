import os
import argparse
import requests
import json
import logging

# Configure logging to show timestamps, log level, and message.
# This will print logs directly to stdout/stderr so they appear in GitHub Actions logs.
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# ----------------------------
# Fetches the GUID of a service from New Relic using its service name
# ----------------------------
def get_entity_guid(service_name, api_key):
    url = "https://api.newrelic.com/graphql"
    headers = {
        "Content-Type": "application/json",
        "API-Key": api_key  # New Relic User API Key
    }

    # GraphQL query to search for the service entity
    query = {
        "query": f"""
        {{
          actor {{
            entitySearch(query: \"name = '{service_name}'\") {{
              results {{
                entities {{
                  name
                  guid
                }}
              }}
            }}
          }}
        }}"""
    }

    response = requests.post(url, headers=headers, json=query)
    data = response.json()

    # Attempt to extract the GUID from the response
    try:
        return data['data']['actor']['entitySearch']['results']['entities'][0]['guid']
    except (KeyError, IndexError):
        logging.error(f"Could not find entity GUID for service: {service_name}")
        logging.info(" Full NerdGraph response:")
        print(json.dumps(data, indent=2))  # Print to stdout so it appears in GitHub logs
        return None

# ----------------------------
# Sends a deployment marker to New Relic for the given service GUID
# ----------------------------
def send_deployment_marker(entity_guid, version, description, user, commit, api_key):
    url = "https://api.newrelic.com/graphql"
    headers = {
        "Content-Type": "application/json",
        "API-Key": api_key
    }

    # GraphQL mutation payload to create a deployment marker
    mutation = {
        "query": f"""
        mutation {{
          changeTrackingCreateDeployment(deployment: {{
            entityGuid: \"{entity_guid}\",
            version: \"{version}\",
            description: \"{description}\",
            user: \"{user}\",
            commit: \"{commit}\",
            groupId: \"apigee-deployment\"
          }}) {{
            deploymentId
          }}
        }}"""
    }

    response = requests.post(url, headers=headers, json=mutation)

    # Log and return result
    if response.status_code == 200:
        logging.info(f"Deployment marker sent for GUID {entity_guid}")
        logging.info(" Marker Response:")
        print(response.text)  # Output full response to GitHub Actions logs
        return True
    else:
        logging.error(f" Failed to send marker. Status {response.status_code}:")
        print(response.text)  # Show full error response
        return False

# ----------------------------
# Main script logic to read proxies and send markers
# ----------------------------
def main(proxy_file, environment, version, user, commit):
    # Read New Relic API key from environment
    api_key = os.getenv("NEW_RELIC_API_KEY")
    if not api_key:
        logging.error(" Missing NEW_RELIC_API_KEY environment variable.")
        exit(1)

    # Verify the proxy list file exists
    if not os.path.exists(proxy_file):
        logging.error(f"Proxy list file not found: {proxy_file}")
        exit(1)

    # Read proxy names from the file, ignoring revision numbers
    with open(proxy_file, "r") as f:
        proxies = [line.strip().split(":")[0] for line in f if line.strip()]

    # Prepare output summary file
    summary_file = f"newrelic_marker_summary_{environment.lower()}.txt"
    with open(summary_file, "w") as summary:
        for proxy_name in proxies:
            service_name = f"apigee_{environment.lower()}_{proxy_name}"  # Build service name like 'apigee_qa_proxyname'
            guid = get_entity_guid(service_name, api_key)  # Look up the GUID from New Relic

            if guid:
                # Send the marker and record success/failure
                success = send_deployment_marker(
                    entity_guid=guid,
                    version=version,
                    description=f"{environment.upper()} deployment for {proxy_name}",
                    user=user,
                    commit=commit,
                    api_key=api_key
                )
                status = "SUCCESS" if success else "FAILED"
            else:
                status = "GUID_NOT_FOUND"

            # Log the result to the summary file and also print to stdout
            summary.write(f"{proxy_name}: {status}\n")
            print(f"{proxy_name}: {status}")

    logging.info(f"📄 Summary written to: {summary_file}")

# ----------------------------
# Entry point for CLI execution
# ----------------------------
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="📡 Send New Relic deployment markers for Apigee proxies.")
    parser.add_argument("--proxy_file", required=True, help="Path to file containing deployed proxies (e.g., qa_success_deployments.txt)")
    parser.add_argument("--environment", required=True, help="Deployment environment (e.g., DEV, QA, STAGE, PROD)")
    parser.add_argument("--version", required=True, help="Version identifier (usually the PR title)")
    parser.add_argument("--user", required=True, help="User who triggered the deployment (e.g., GitHub actor)")
    parser.add_argument("--commit", required=True, help="Git commit SHA associated with the deployment")

    args = parser.parse_args()

    main(args.proxy_file, args.environment, args.version, args.user, args.commit)
