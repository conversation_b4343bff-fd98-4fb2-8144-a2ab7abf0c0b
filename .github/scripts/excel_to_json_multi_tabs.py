import pandas as pd
import json
import argparse
from pathlib import Path

def excel_to_json(excel_file, proxy_dir, output_file):
    """Convert Excel with multiple tabs to JSON, filtering proxies based on actual folders."""
    
    # Load Excel file and read all sheets
    xls = pd.ExcelFile(excel_file)
    all_data = []

    # Get actual proxy folders that exist
    proxy_root = Path(proxy_dir)
    existing_proxies = {
        folder.name for folder in proxy_root.iterdir() if folder.is_dir() and (folder / "apiproxy").exists()
    }
    print(f"🔍 Detected proxy folders: {existing_proxies}")

    for sheet_name in xls.sheet_names:
        print(f"📄 Processing sheet: {sheet_name}")
        df = pd.read_excel(xls, sheet_name=sheet_name)

        # Ensure required columns exist
        required_columns = ["App Name", "Product Name", "Proxies"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ Missing columns in {sheet_name}: {missing_columns}. Skipping sheet.")
            continue

        # Process each row
        for _, row in df.iterrows():
            app_name = row["App Name"]
            product_name = row["Product Name"]
            proxies = str(row["Proxies"]).strip().split("\n")  # Split proxies by newline
            
            # Remove empty entries and strip spaces
            proxies = {p.strip() for p in proxies if p.strip()}  # Use a set to remove duplicates

            # Filter proxies based on actual folders
            valid_proxies = sorted(proxies.intersection(existing_proxies))

            # Only add entries that have valid proxies
            if valid_proxies:
                all_data.append({
                    "App Name": app_name,
                    "Product Name": product_name,
                    "Proxies": valid_proxies
                })

    # Write output to JSON file
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully created {output_file} with {len(all_data)} valid entries")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Convert Excel (with multiple tabs) to JSON, filtering proxies based on existing folders.")
    parser.add_argument("--excel_file", required=True, help="Path to the Excel file.")
    parser.add_argument("--proxy_dir", required=True, help="Path to the directory containing proxy folders.")
    parser.add_argument("--output_file", required=True, help="Path to the output JSON file.")

    args = parser.parse_args()
    excel_to_json(args.excel_file, args.proxy_dir, args.output_file)
