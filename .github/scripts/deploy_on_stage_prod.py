import os
import json
import argparse
import requests
import logging
import subprocess
import time
import shutil

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

def run_command(command):
    """Execute shell commands and return output."""
    logging.info(f"Running command: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        logging.error(f"Command failed: {result.stderr}")
    return result.stdout.strip()

def fetch_deployed_revisions(apigee_org, environment, proxy_name, access_token):
    """Fetch currently deployed revisions for a specific proxy."""
    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/apis/{proxy_name}/deployments"
    command = f'curl -s -H "Authorization: Bearer {access_token}" "{url}"'
    response = run_command(command)

    try:
        deployments = json.loads(response)
        if "deployments" in deployments:
            return [int(dep["revision"]) for dep in deployments["deployments"]]
    except json.JSONDecodeError:
        logging.error(f"Failed to parse deployed revisions for {proxy_name}. Response: {response}")

    return []

def zip_proxy_folder(proxy_name, repo_path):
    """Ensure proxy folders are extracted properly before zipping."""
    proxy_dir = os.path.join(repo_path, proxy_name)
    proxy_path = os.path.join(proxy_dir, "apiproxy")
    zip_file = os.path.join(proxy_dir, f"{proxy_name}.zip")  # ZIP file inside proxy dir
    final_zip_path = os.path.join(repo_path, f"{proxy_name}.zip")  # Move to repo_path

    # Debug: Log structure before zipping
    logging.info(f"🔍 Checking extracted structure before zipping: {proxy_name}")
    run_command(f"ls -R {proxy_dir} || echo '⚠️ Proxy folder missing!'")

    # Ensure apiproxy folder exists
    if not os.path.exists(proxy_path):
        logging.error(f"❌ Expected 'apiproxy/' folder missing for {proxy_name} at {proxy_path}")
        return None

    # ✅ Create ZIP in proxy directory
    zip_command = f"cd {proxy_dir} && zip -r {proxy_name}.zip apiproxy -x '*.DS_Store'"
    zip_result = run_command(zip_command)

    # ✅ Verify ZIP creation
    if not os.path.exists(zip_file):
        logging.error(f"❌ ZIP file creation failed for {proxy_name}. Command output: {zip_result}")
        return None

    # ✅ Move ZIP to repo_path for consistency
    shutil.move(zip_file, final_zip_path)

    logging.info(f"✅ Created ZIP bundle at: {final_zip_path}")
    return final_zip_path

def upload_proxy_if_missing(apigee_org, proxy_name, repo_path, access_token):
    """Uploads the proxy bundle if it doesn't exist in Apigee."""
    zip_file = os.path.join(repo_path, f"{proxy_name}.zip")
    
    if not os.path.exists(zip_file):
        logging.error(f"❌ ZIP file for {proxy_name} not found! Skipping upload.")
        return None

    upload_url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/apis?action=import&name={proxy_name}"
    upload_command = f'curl -X POST "{upload_url}" -H "Authorization: Bearer {access_token}" -F file=@{zip_file}'
    upload_response = run_command(upload_command)

    try:
        upload_json = json.loads(upload_response)
        new_revision = upload_json.get("revision")
        if not new_revision:
            logging.error(f"Upload response did not return a revision. Full response: {upload_response}")
            return None
        logging.info(f"✅ Uploaded proxy {proxy_name}. New revision: {new_revision}")
        return new_revision
    except json.JSONDecodeError:
        logging.error(f"❌ Failed to upload proxy {proxy_name}. Full response: {upload_response}")
        return None
def deploy_proxy(apigee_org, environment, access_token, proxy_name, revision):
    """Deploys the given proxy revision to Apigee if not already deployed."""
    logging.info(f"🚀 Deploying {proxy_name} revision {revision} to {environment}...")

    url = f"https://apigee.googleapis.com/v1/organizations/{apigee_org}/environments/{environment}/apis/{proxy_name}/revisions/{revision}/deployments?override=true"
    command = f'curl -s -X POST "{url}" -H "Authorization: Bearer {access_token}" -H "Content-Type: application/json"'

    result = run_command(command)
    if 'error' in result.lower():
        logging.error(f"❌ Deployment failed for {proxy_name} revision {revision}. Full response: {result}")
        return False

    logging.info(f"✅ Successfully deployed {proxy_name} revision {revision} to {environment}.")
    return True

def deploy_proxies(repo_path, apigee_org, environment, access_token, output_mapping_file):
    """Deploy only proxies that are in the artifact. If exists, upload & deploy. If missing, create, upload & deploy."""
    
    # List all proxies in the artifact directory
    artifact_proxies = {proxy for proxy in os.listdir(repo_path) if os.path.isdir(os.path.join(repo_path, proxy))}
    
    logging.info(f"🔍 Found {len(artifact_proxies)} proxies in the artifact for deployment: {', '.join(artifact_proxies)}")

    new_mapping = {}

    success_file = f"{environment}_success_deployments.txt"
    skipped_file = f"{environment}_skipped_proxies.txt"
    error_file = f"{environment}_deployment_errors.txt"
    rollback_file = f"{environment}_deployment_revisions.json"

    # Clear previous log files
    for file in [success_file, skipped_file, error_file]:
        open(file, "w").close()

    for proxy_name in artifact_proxies:
        logging.info(f"🚀 Processing proxy: {proxy_name}")

        # Fetch previously deployed revisions in Apigee
        previous_revisions = fetch_deployed_revisions(apigee_org, environment, proxy_name, access_token)
        previous_revision = max(previous_revisions) if previous_revisions else "N/A"
        
        logging.info(f"📌 Existing deployed revision for {proxy_name} in {environment}: {previous_revision}")
        
        # Ensure ZIP exists before uploading
        zip_file = zip_proxy_folder(proxy_name, repo_path)
        if not zip_file:
            logging.error(f"❌ Skipping upload: ZIP file creation failed for {proxy_name}")
            continue  # Skip to the next proxy if ZIP fails
        
        # Upload the proxy bundle
        latest_revision = upload_proxy_if_missing(apigee_org, proxy_name, repo_path, access_token)

        if latest_revision:
            if str(latest_revision) == str(previous_revision):
                logging.info(f"✅ {proxy_name} is already at latest revision {latest_revision}. Skipping deployment.")
                with open(skipped_file, "a") as f:
                    f.write(f"{proxy_name}:{latest_revision}\n")
                continue

            # Deploy the proxy
            logging.info(f"🚀 Deploying {proxy_name} revision {latest_revision} to {environment}...")
            success = deploy_proxy(apigee_org, environment, access_token, proxy_name, latest_revision)

            if success:
                with open(success_file, "r+") as f:
                    existing_lines = f.readlines()  
                    entry = f"{proxy_name}:{latest_revision}\n"
                    if entry not in existing_lines: 
                        f.write(entry)  
                logging.info(f"{proxy_name} revision {latest_revision} recorded as successfully deployed.")
            else:
                logging.error(f"❌ Deployment failed for {proxy_name} revision {latest_revision}.")
                with open(error_file, "a") as f:
                    f.write(f"{proxy_name}:{latest_revision}\n")

            new_mapping[proxy_name] = {
                "previous_revision": previous_revision,
                "latest_revision": latest_revision
            }
    if success:
        with open(success_file, "a") as f:
            f.write(f"{proxy_name}:{latest_revision}\n")
        logging.info(f"✅ {proxy_name} revision {latest_revision} recorded as successfully deployed.")

    # Save rollback mapping
    with open(rollback_file, "w") as f:
        json.dump(new_mapping, f, indent=4)

    logging.info(f"📄 Deployment completed. Updated {environment} deployment mapping in {rollback_file}.")


def main(repo_path, apigee_org, environment, access_token, output_mapping_file):
    """Main function for deploying proxies and products to the specified environment."""
    logging.info(f"Starting {environment.upper()} deployment...")
    deploy_proxies(repo_path, apigee_org, environment, access_token, output_mapping_file)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Deploy API Proxies and Products to a specified environment.")
    parser.add_argument("--repo_path", required=True, help="Path to the repository containing proxy folders.")
    parser.add_argument("--apigeex_org", required=True, help="Apigee organization name for the environment.")
    parser.add_argument("--environment", required=True, choices=["stage", "prod"], help="Apigee environment (stage or prod).")
    parser.add_argument("--access_token", required=True, help="Access token for Apigee API.")
    parser.add_argument("--output_mapping_file", required=True, help="File to store updated deployment mapping.")

    args = parser.parse_args()
    main(
        args.repo_path,
        args.apigeex_org,
        args.environment,
        args.access_token,
        args.output_mapping_file
    )
