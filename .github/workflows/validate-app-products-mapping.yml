name: 🛠 Validate & Fix Apigee Apps & Product Mappings
run-name: 🛠 Validate apps in ${{ inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Apigee environment (dev, qa, stage, prod)'
        required: true

jobs:
  validate-app-products:
    name: Validate & Fix App Product Mapping
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install Python & Tools
        run: |
          sudo apt-get update && sudo apt-get install -y python3 python3-pip jq
          pip3 install requests pandas openpyxl

      - name: Set Org, Service Account, Developer Email
        id: envmap
        run: |
          if [[ "${{ inputs.environment }}" == "dev" || "${{ inputs.environment }}" == "qa" ]]; then
            echo "org=${{ vars.APIGEEX_DEV_ORG }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
            echo "developer_email=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.environment }}" == "stage" ]]; then
            echo "org=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
            echo "developer_email=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.environment }}" == "prod" ]]; then
            echo "org=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
            echo "developer_email=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_OUTPUT
          else
            echo "Invalid environment input!"
            exit 1
          fi

      - name: Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ steps.envmap.outputs.wif }}
          service_account: ${{ steps.envmap.outputs.sa }}

      - name: Convert Excel to JSON
        run: |
          python3 .github/scripts/excel-json-mapping.py \
            --excel_file Apigee_Apps_Summary.xlsx \
            --output_file product-mappings/temp_products.json

      - name: Validate and Fix Apps and Product Associations
        run: |
          python3 .github/scripts/validate_and_fix_apps.py \
            --input_json_file product-mappings/temp_products.json \
            --apigee_org "${{ steps.envmap.outputs.org }}" \
            --developer_email "${{ steps.envmap.outputs.developer_email }}" \
            --access_token "${{ steps.auth.outputs.access_token }}"
