name: Replicate Apigee Environment
run-name: Cloing Apigee Environment ${{ inputs.from_env }} to ${{ inputs.to_env }}
on:
  workflow_dispatch:
    inputs:
      from_org:
        description: "Source Apigee Organization ID"
        required: true
      from_sa:
        description: "Source Service Account Email"
        required: true
      from_env:
        description: "Source  Environment"
        required: true
      to_org:
        description: "Target Apigee Organization ID"
        required: false
      to_env:
        description: "Target  Environment"
        required: true
      to_sa:
        description: "Target Service Account Email"
        required: false

jobs:
  fetch-configs:
    name: Fetch Configurations from ${{ inputs.from_org }} (${{ inputs.from_env }})
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    steps:
      - name: Authenticate with GCP (Source)
        id: auth_source
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ inputs.from_sa }}

      # Fetch Target Servers
      - name: Fetch Target Servers
        run: |
          mkdir -p target_servers
          TARGET_SERVERS=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/targetservers" | jq -r '.[]')

          for SERVER in $TARGET_SERVERS; do
            curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/targetservers/$SERVER" > "target_servers/$SERVER.json"
          done

      # Fetch KVMs with Entries
      - name: Fetch Key-Value Maps
        run: |
          mkdir -p kvms
          echo "Fetching list of KVMs from source environment..."

          # Fetch list of KVMs
          KVM_LIST=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/keyvaluemaps" | jq -r '.[]')

          for KVM in $KVM_LIST; do
            echo "Processing KVM: $KVM"

            # Create a minimal JSON file for KVM creation
            echo "{\"name\": \"$KVM\"}" > "kvms/$KVM.json"

            echo "Fetching entries for KVM: $KVM"
            curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/keyvaluemaps/$KVM/entries" > "kvms/$KVM-entries.json"
          done

      # Fetch API Proxies (Download API Bundles)
      - name: Fetch and Download API Proxies
        run: |
          mkdir -p apis
          
          # Fetch the list of API proxies
          APIS=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apis" | jq -r '.proxies[].name')

          if [[ -z "$APIS" ]]; then
            echo "No APIs found. Exiting..."
            exit 0
          fi

          for API in $APIS; do
            echo "Fetching latest deployed revision for API: $API..."

            # Get the latest deployed revision
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/apis/$API/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            if [[ -z "$LATEST_REV" ]] || [[ "$LATEST_REV" == "null" ]]; then
              echo "No active deployed revision found for $API. Skipping..."
              continue
            fi

            echo "Downloading API Proxy: $API (Revision: $LATEST_REV)..."

            curl -s -L -o "apis/${API}.zip" \
              -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apis/$API/revisions/$LATEST_REV?format=bundle"

            if [[ ! -f "apis/${API}.zip" ]]; then
              echo "Failed to download $API. Skipping..."
            fi
          done

      # Fetch Shared Flows
      - name: Fetch and Download Shared Flows
        run: |
          mkdir -p shared_flows
          
          # Fetch the list of shared flows
          SHARED_FLOWS=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/sharedflows" | jq -r '.sharedFlows[].name')

          if [[ -z "$SHARED_FLOWS" ]]; then
            echo "No shared flows found. Exiting..."
            exit 0
          fi

          for FLOW in $SHARED_FLOWS; do
            echo "Fetching latest revision for Shared Flow: $FLOW..."

            # Get the latest deployed revision
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/environments/${{ inputs.from_env }}/sharedflows/$FLOW/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            if [[ -z "$LATEST_REV" ]] || [[ "$LATEST_REV" == "null" ]]; then
              echo "No active deployed revision found for $FLOW. Skipping..."
              continue
            fi

            echo "Downloading Shared Flow: $FLOW (Revision: $LATEST_REV)..."

            curl -s -L -o "shared_flows/${FLOW}.zip" \
              -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/sharedflows/$FLOW/revisions/$LATEST_REV?format=bundle"

            if [[ ! -f "shared_flows/${FLOW}.zip" ]]; then
              echo "Failed to download $FLOW. Skipping..."
            fi
          done

      # Fetch API Products
      - name: Fetch API Products
        run: |
          mkdir -p products

          # Fetch API Product names correctly
          PRODUCTS=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apiproducts" | jq -r '.apiProduct[].name')

          if [[ -z "$PRODUCTS" ]]; then
            echo "No API Products found. Exiting..."
            exit 0
          fi

          for PRODUCT in $PRODUCTS; do
            echo "Fetching API Product: $PRODUCT"

            curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apiproducts/$PRODUCT" > "products/$PRODUCT.json"

            echo "Successfully fetched API Product: $PRODUCT"
          done

      # Fetch Apps and Store App Details
      - name: Fetch and Store Apps
        run: |
          mkdir -p apps

          # Fetch the list of App IDs
          APP_IDS=$(curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apps" | jq -r '.app[].appId')

          if [[ -z "$APP_IDS" ]]; then
            echo "No apps found. Exiting..."
            exit 0
          fi

          for APP_ID in $APP_IDS; do
            echo "Fetching details for App ID: $APP_ID"

            curl -s -H "Authorization: Bearer ${{ steps.auth_source.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.from_org }}/apps/$APP_ID" > "apps/$APP_ID.json"

            if [[ ! -s "apps/$APP_ID.json" ]]; then
              echo "Failed to fetch details for App ID: $APP_ID. Skipping..."
            else
              echo "Successfully fetched app details for: $APP_ID"
            fi
          done

      - name: Store Configurations as Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: apigee-config-backup
          path: |
            target_servers/*.json
            kvms/*.json
            kvms/*-entries.json
            shared_flows/*.zip
            apis/*.zip
            products/*.json
            apps/*.json

  clone-approval:
    name: Clone Approval
    needs: [ fetch-configs ]
    runs-on: ubuntu-latest
    environment: ${{ inputs.to_env }}
    steps:
      - name: Clone Approval
        run: |
          echo "Clone Approval"

  connection-check:
    name: Connection Check with ${{ inputs.to_env }}
    needs: [ clone-approval ]
    runs-on: ubuntu-latest
    environment: ${{ inputs.to_env }}
    permissions:
      id-token: write
      contents: write
    steps:
      - name: Authenticate with GCP (Target)
        id: auth_target
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ inputs.to_sa }}
  
  deploy-approval:
    name: Approval to deploy on ${{ inputs.to_env }}
    needs: [ connection-check ]
    runs-on: ubuntu-latest
    environment: ${{ inputs.to_env }}
    steps:
      - name: Clone Approval
        run: |
          echo "Deploy Approval"

  deploy-configs:
    name: Deploy in ${{ inputs.to_org }} (${{ inputs.to_env }})
    needs: [ deploy-approval ]
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    steps:
      - name: Authenticate with GCP (Target)
        id: auth_target
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ inputs.to_sa }}

      - name: Download Stored Artifacts
        uses: actions/download-artifact@v4
        with:
          name: apigee-config-backup
      
      - name: List Files
        run : |
          ls -l
          tree -d

      - name: Deploy Target Servers
        run: |
          for FILE in target_servers/*.json; do
            SERVER_NAME=$(basename "$FILE" .json)
            echo "Deploying Target Server: $SERVER_NAME"

            curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              -H "Content-Type: application/json" -d "@$FILE" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/targetservers"
          done

      # Deploy KVMs to target
      - name: Deploy KVMs
        run: |
          for FILE in kvms/*.json; do
            KVM_NAME=$(basename "$FILE" .json)

            # Skip processing entry files
            if [[ "$KVM_NAME" == *"-entries" ]]; then
              echo "Skipping KVM entry file: $KVM_NAME"
              continue
            fi

            ENTRIES_FILE="kvms/${KVM_NAME}-entries.json"
            echo "Processing KVM: $KVM_NAME"

            # Check KVM existence using direct HTTP status code
            STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" -X GET \
              -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps/$KVM_NAME")

            # Create KVM if it does not exist
            if [[ "$STATUS_CODE" -ne 200 ]]; then
              echo "Creating new KVM: $KVM_NAME"

              CREATE_RESPONSE=$(curl -s -o create_response.json -w "%{http_code}" -X POST \
                -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" \
                -d "{\"name\": \"$KVM_NAME\"}" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps")

              if [[ "$CREATE_RESPONSE" -ne 201 ]]; then
                echo "Failed to create KVM: $KVM_NAME (HTTP $CREATE_RESPONSE)"
                cat create_response.json
                exit 1
              fi
            else
              echo "KVM $KVM_NAME already exists. Proceeding with entry updates."
            fi

            # Process entries if the entries file exists
            if [[ -f "$ENTRIES_FILE" ]]; then
              echo "Processing entries for KVM: $KVM_NAME"

              # Extract valid key-value pairs
              jq -c '.keyValueEntries[]' "$ENTRIES_FILE" | while read -r ENTRY; do
                ENTRY_NAME=$(echo "$ENTRY" | jq -r '.name')
                ENTRY_VALUE=$(echo "$ENTRY" | jq -r '.value | @json')

                echo "Adding entry: $ENTRY_NAME"
                ENTRY_RESPONSE=$(curl -s -o entry_response.json -w "%{http_code}" -X POST \
                  -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                  -H "Content-Type: application/json" \
                  -d "{\"name\": \"$ENTRY_NAME\", \"value\": $ENTRY_VALUE}" \
                  "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps/$KVM_NAME/entries")

                # ✅ Fix: Allow HTTP 201 (Created) as a success response
                if [[ "$ENTRY_RESPONSE" -ne 200 && "$ENTRY_RESPONSE" -ne 201 ]]; then
                  echo "Failed to add entry $ENTRY_NAME (HTTP $ENTRY_RESPONSE)"
                  cat entry_response.json
                  exit 1
                fi
              done
            else
              echo "No entries file found for $KVM_NAME"
            fi
          done

      - name: Deploy Shared Flows
        run: |
          for FILE in shared_flows/*.zip; do
            FLOW_NAME=$(basename "$FILE" .zip)
            echo "Deploying Shared Flow: $FLOW_NAME"

            curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              -F "file=@$FILE" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/sharedflows?action=import&name=$FLOW_NAME"

            # Get latest revision number
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/sharedflows/$FLOW_NAME/revisions" | \
              jq -r '.[-1]')

            echo "Deploying Shared Flow: $FLOW_NAME Revision: $LATEST_REV"
            curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/sharedflows/$FLOW_NAME/revisions/$LATEST_REV/deployments?override=true"
          done

 # Deploy API Proxies to Apigee
      - name: Deploy API Proxies
        run: |
          for FILE in apis/*.zip; do
            PROXY_NAME=$(basename "$FILE" .zip)
            echo "Uploading API Proxy: $PROXY_NAME"

            RESPONSE=$(curl -s -o response.json -w "%{http_code}" -X POST \
              -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              -F "file=@$FILE" \
              "https://apigee.googleapis.com/v1/organizations/cei-xzv4pljl/apis?action=import&name=$PROXY_NAME")

            if [[ "$RESPONSE" -ne 200 && "RESPONSE" -ne 201 ]]; then
              echo "Failed to upload API Proxy: $PROXY_NAME (HTTP $RESPONSE)"
              cat response.json
              exit 1
            fi

            echo "Deploying API Proxy: $PROXY_NAME"
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/cei-xzv4pljl/apis/$PROXY_NAME/revisions" | \
              jq -r '.[-1]')

            curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/cei-xzv4pljl/environments/sandbox/apis/$PROXY_NAME/revisions/$LATEST_REV/deployments?override=true"

          done  # ✅ Removed nested loop, keeping only one `done`
          
      - name: Deploy API Products
        run: |
          for FILE in products/*.json; do
            PRODUCT_NAME=$(jq -r '.name' "$FILE")
            echo "Checking if API Product exists: $PRODUCT_NAME in ${{ inputs.to_env }}"

            RESPONSE=$(curl -s -o response.json -w "%{http_code}" -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts/$PRODUCT_NAME")

            HTTP_STATUS=$(cat response.json | jq -r '.error.code')

            # Remove metadata fields (createdAt, lastModifiedAt) and update environment dynamically
            UPDATED_PAYLOAD=$(jq --arg target_env "${{ inputs.to_env }}" \
              'del(.createdAt, .lastModifiedAt) | .environments = [$target_env]' "$FILE")

            echo "$UPDATED_PAYLOAD" > updated_product.json

            if [[ "$HTTP_STATUS" == "404" ]]; then
              echo "Creating API Product: $PRODUCT_NAME"
              curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "@updated_product.json" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts"
            else
              echo "Updating API Product: $PRODUCT_NAME"
              curl -X PUT -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "@updated_product.json" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts/$PRODUCT_NAME"
            fi
          done

      - name: Deploy All Apigee Apps
        run: |
          TARGET_SERVICE_ACCOUNT="apigeecicdsa-${{ inputs.to_env }}@default-440318.iam.gserviceaccount.com"
      
          # Fetch Developer ID for the target service account
          echo "Fetching Developer ID for Service Account: $TARGET_SERVICE_ACCOUNT"
          TARGET_DEVELOPER_RESPONSE=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_SERVICE_ACCOUNT")
      
          TARGET_DEVELOPER_ID=$(echo "$TARGET_DEVELOPER_RESPONSE" | jq -r '.developerId')
      
          if [[ -z "$TARGET_DEVELOPER_ID" || "$TARGET_DEVELOPER_ID" == "null" ]]; then
            echo "Error: Could not fetch Developer ID for $TARGET_SERVICE_ACCOUNT in ${{ inputs.to_env }}. Exiting."
            exit 1
          fi
      
          echo "Developer ID for target service account: $TARGET_DEVELOPER_ID"
      
          # Iterate over all JSON files in the apps directory
          for FILE in apps/*.json; do
            APP_NAME=$(jq -r '.name' "$FILE")
      
            if [[ -z "$APP_NAME" || "$APP_NAME" == "null" ]]; then
              echo "Skipping file $FILE - No valid app name found."
              continue
            fi
      
            echo "Processing App: $APP_NAME"
      
            # Check if the App already exists
            APP_STATUS=$(curl -s -o response.json -w "%{http_code}" -X GET \
              -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME")
      
            if [[ "$APP_STATUS" -eq 200 ]]; then
              echo "App $APP_NAME already exists. Skipping creation."
            else
              echo "Creating App: $APP_NAME under Developer ID: $TARGET_DEVELOPER_ID"
      
              # Prepare App payload (removing unnecessary fields and updating Developer ID)
              UPDATED_APP_PAYLOAD=$(jq --arg devId "$TARGET_DEVELOPER_ID" \
                'del(.createdAt, .lastModifiedAt, .credentials, .developerId) | .developerId = $devId' "$FILE")
      
              # Create the App
              CREATE_RESPONSE=$(curl -s -o create_response.json -w "%{http_code}" -X POST \
                -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "$UPDATED_APP_PAYLOAD" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps")
      
              if [[ "$CREATE_RESPONSE" -ne 201  ]]; then
                echo "Failed to create App: $APP_NAME (HTTP $CREATE_RESPONSE)"
                cat create_response.json
                exit 1
              fi
            fi
      
            # Retrieve Consumer Key for the created App
            echo "Fetching Consumer Key for App: $APP_NAME"
            CONSUMER_KEY=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME" | \
              jq -r '.credentials[0].consumerKey')
      
            if [[ -z "$CONSUMER_KEY" || "$CONSUMER_KEY" == "null" ]]; then
              echo "Error: Could not retrieve Consumer Key for $APP_NAME. Exiting."
              exit 1
            fi
      
            # Extract API Products from the App JSON
            PRODUCTS=$(jq -r '.credentials[].apiProducts[].apiproduct' "$FILE" | sort -u)
      
            # Associate API Products with the App
            if [[ ! -z "$PRODUCTS" ]]; then
              echo "Associating API Products with App: $APP_NAME"
              PRODUCT_PAYLOAD=$(jq -n --argjson products "$(echo "$PRODUCTS" | jq -R -s -c 'split("\n")[:-1]')" '{"apiProducts": $products}')
      
              curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "$PRODUCT_PAYLOAD" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME/keys/$CONSUMER_KEY"
            else
              echo "No API Products found for $APP_NAME. Skipping API Product association."
            fi
          done
            
      - name: Notify Success
        run: |
          echo "Deployment of Apigee configurations to ${{ inputs.to_org }} (${{ inputs.to_env }}) is complete!"