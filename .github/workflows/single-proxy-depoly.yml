name: Proxy Deployment Pipeline (DEV → QA → Stage → Prod)
run-name: ${{ github.actor }} triggered proxy deployment for ${{inputs.proxy_name}}

on:
  workflow_dispatch:
    inputs:
      proxy_name:
        description: "Name of the Apigee proxy (e.g., CCI-sc-wireless-example)"
        required: true

jobs:
  # Step 1: Download from DEV
  download-from-dev:
    name: 📥 Download Proxy from DEV
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (DEV)
        id: auth_dev
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      - name: 📥 Download Proxy ZIP from DEV
        run: |
          mkdir -p proxies
          PROXY="${{ inputs.proxy_name }}"

          echo "🔍 Fetching latest deployed revision for $PROXY in DEV..."
          LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_dev.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/dev/apis/$PROXY/deployments" \
            | jq -r '.deployments[].revision' | sort -nr | head -1)

          if [ -z "$LATEST_REV" ] || [ "$LATEST_REV" == "null" ]; then
            echo "❌ No deployed revision found for $PROXY in DEV."
            exit 1
          fi

          echo "✅ Found revision $LATEST_REV. Downloading ZIP..."
          curl -s -L -o "proxies/${PROXY}.zip" \
            -H "Authorization: Bearer ${{ steps.auth_dev.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/apis/$PROXY/revisions/$LATEST_REV?format=bundle"

      - name: 📤 Upload Proxy ZIP Artifact
        uses: actions/upload-artifact@v4
        with:
          name: proxy-artifact
          path: proxies/
          retention-days: 360

  QA_APPROVAL:
    name: 🛂 Approve QA Deployment
    runs-on: ubuntu-latest
    needs: [ download-from-dev ]
    environment: "QA_Approval"
    steps:
      - name: Wait for QA approval
        run: echo "Waiting for QA approval..."

  deploy-to-qa:
    name: 🚀 Deploy Same Proxy Revision to QA
    needs: QA_APPROVAL
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (QA)
        id: auth_qa
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      - name: 🚀 Deploy Proxy Revision to QA
        run: |
          PROXY="${{ inputs.proxy_name }}"
          DEV_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/dev/apis/$PROXY/deployments" \
            | jq -r '.deployments[].revision' | sort -nr | head -1)

          QA_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/qa/apis/$PROXY/deployments" \
            | jq -r '.deployments[].revision' | sort -nr | head -1)

          if [ "$DEV_REV" == "$QA_REV" ]; then
            echo "⚠️ $PROXY revision $DEV_REV already deployed in QA. Skipping."
            exit 0
          fi

          echo "🚀 Deploying $PROXY revision $DEV_REV to QA..."
          curl -X POST \
            -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/qa/apis/$PROXY/revisions/$DEV_REV/deployments?override=true"

  stage_deploy_approval:
    name: 🛂 Approve Stage Deployment
    runs-on: ubuntu-latest
    needs: [ deploy-to-qa ]
    environment: "STAGE_Deploy_Approval"
    steps:
      - name: Wait for stage approval
        run: echo "Waiting for stage approval..."

  deploy-to-stage:
    name: 🚀 Deploy Proxy to Stage
    needs: stage_deploy_approval
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (Stage)
        id: auth_stage
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_STAGE }}

      - name: 📥 Download Proxy ZIP Artifact
        uses: actions/download-artifact@v4
        with:
          name: proxy-artifact
          path: proxies/

      - name: 🚀 Upload and Deploy to Stage
        run: |
          PROXY="${{ inputs.proxy_name }}"
          ZIP="proxies/${PROXY}.zip"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
            -F "file=@$ZIP" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/apis?action=import&name=$PROXY"

          NEW_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/apis/$PROXY/revisions" | jq -r 'max')

          curl -X POST \
            -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/environments/stage/apis/$PROXY/revisions/$NEW_REV/deployments?override=true"

  PROD_APPROVAL:
    name: 🛂 Stage Approval
    runs-on: ubuntu-latest
    needs: [ deploy-to-stage ]
    environment: "PROD_Approval"
    steps:
      - name: Wait for prod approval
        run: echo "Waiting for prod approval..."

  PROD_DEPLOYMENT_APPROVAL:
    name: 🛂 Approve Prod Deployment
    runs-on: ubuntu-latest
    needs: [ PROD_APPROVAL ]
    environment: "PROD_Deploy_Approval"
    steps:
      - name: Wait for prod deploy approval
        run: echo "Waiting for prod deploy approval..."

  deploy-to-prod:
    name: 🚀 Deploy Proxy to Prod
    needs: PROD_DEPLOYMENT_APPROVAL
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (Prod)
        id: auth_prod
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_PROD }}

      - name: 📥 Download Proxy ZIP Artifact
        uses: actions/download-artifact@v4
        with:
          name: proxy-artifact
          path: proxies/

      - name: 🚀 Upload and Deploy to Prod
        run: |
          PROXY="${{ inputs.proxy_name }}"
          ZIP="proxies/${PROXY}.zip"

          curl -s -X POST \
            -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
            -F "file=@$ZIP" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/apis?action=import&name=$PROXY"

          NEW_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/apis/$PROXY/revisions" | jq -r 'max')

          curl -X POST \
            -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/environments/prod/apis/$PROXY/revisions/$NEW_REV/deployments?override=true"
