name: 🚀 Apigee CI/CD Pipeline
run-name: 🚀 ${{inputs.branch}}-${{inputs.pr_title}}-${{ inputs.repository }}-${{inputs.commit_author_name}}

on:
  workflow_dispatch:
    inputs:
      repository:
        description: 'Name of the repository triggering this workflow'
        required: true
      branch:
        description: 'Branch triggering this workflow (e.g., dev or main)'
        required: true
      commit_sha:
        description: 'Commit SHA triggering this workflow'
        required: true
      commit_author:
        description: 'Author of the triggering commit'
        required: true
      pr_title:
        description: 'Pull Request Title or Manual Trigger'
        required: true
      commit_author_name:
        description: 'Name of the Commit Owner'
        required: true
      commit_author_email:
        description: 'Commit Owner Email'
        required: true
      pr_number:
        description: ' Number of the PR that triggered this run'
        required: true
      artifact_id:
        description: 'Proxies artifact id'
        required: true
      run_id:
        description: 'Proxies run id'
        required: true

env:
  APIGEE_BASE_PATH: /v1/organizations
  APIGEE_VIRTUAL_HOST: secure
  APIGEE_ROUTING_RULE: default
  APIGEE_ROUTE_TYPE: request
  APIGEE_TARGET_TYPE: httpTargetConnection
jobs:
  # Job 1: Deploy Proxies to DEV Environment
  deploy-proxies-dev:
    name: 🚀 Deploy Proxies to DEV
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
      actions: read
    outputs:
      deployment_status: ${{ steps.check_output.outputs.status }}
      artifact_name: ${{ steps.set_artifact_name.outputs.artifact_name }}
    steps:
      # Step 1: Print Inputs for Debugging and Traceability
      - name: 📝 Print Workflow Inputs
        run: |
          echo "Repository: ${{ inputs.repository }}"
          echo "Branch: ${{ inputs.branch }}"
          echo "Commit SHA: ${{ inputs.commit_sha }}"
          echo "Commit Author: ${{ inputs.commit_author }}"
          echo "PR Title: ${{ inputs.pr_title }}"
          echo "PR Number: ${{ inputs.pr_number }}"
          echo "Commit Author Email: ${{ inputs.commit_author_email }}"
          echo "Commit Autho Name: ${{ inputs.commit_author_name }}"

      # Step 2: Checkout Code from the Triggering Repository
      - name: 📁 Checkout Code
        uses: actions/checkout@v4
        with:
          path: child-repo
          repository: ${{ github.event.inputs.repository }}
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.APIGEEX_CHILD_REPO_PAT }}

      # Step 3: Checkout self repo
      - name: 📁 Checkout Full Repository
        uses: actions/checkout@v4
        with:
          path: central-repo

      # Step 4: Authenticate with GCP
      - name: 🔐 Authenticate with GCP
        id: non_prodAuth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      # Step 5: Install Dependencies
      - name: 🚧 Install Required Tools
        run: |
          sudo apt-get update && sudo apt-get install -y jq zip python3 python3-pip
          pip3 install requests
          pip3 install pandas
          pip3 install openpyxl
      # Step 6: Get the Artifact Name from the Artifact ID
      - name: 🔍 Get Artifact Name from ID
        id: set_artifact_name
        run: |
          # Get specific artifact details using known ID
          ARTIFACT_JSON=$(curl -s -H "Authorization: Bearer ${{ secrets.APIGEEX_CHILD_REPO_PAT }}" \
            -H "Accept: application/vnd.github+json" \
            "https://api.github.com/repos/${{ inputs.repository }}/actions/artifacts/${{ inputs.artifact_id }}")

          
          # Extract name from JSON response
          ARTIFACT_NAME=$(echo "$ARTIFACT_JSON" | jq -r '.name')
          
          # Validate response
          if [[ -z "$ARTIFACT_NAME" || "$ARTIFACT_NAME" == "null" ]]; then
            echo "::error::Failed to retrieve artifact name for ID ${{ inputs.artifact_id }}"
            echo "API Response: $ARTIFACT_JSON"
            exit 1
          fi
          
          echo "artifact_name=$ARTIFACT_NAME" >> $GITHUB_ENV
          echo "artifact_name=$ARTIFACT_NAME" >> $GITHUB_OUTPUT
          echo "Resolved Artifact Name: $ARTIFACT_NAME"
        shell: bash

      # Step 7: Download Artifact and Extract
      - name: 📥 📦️ Download & Extract Proxies Artifact
        run: |
          curl -L -o proxies.zip -H "Authorization: Bearer ${{ secrets.APIGEEX_CHILD_REPO_PAT }}" \
            "https://api.github.com/repos/${{inputs.repository}}/actions/artifacts/${{ inputs.artifact_id }}/zip"

          mkdir -p ./proxies
          unzip proxies.zip -d ./proxies

          # Check if the extracted folder contains another ZIP and extract it
          INNER_ZIP=$(find ./proxies -name "*.zip" | head -n 1)
          if [[ -f "$INNER_ZIP" ]]; then
            echo "🔍 Found inner ZIP: $INNER_ZIP, extracting..."
            unzip -o "$INNER_ZIP" -d ./proxies
            rm -f "$INNER_ZIP"  # Remove the redundant ZIP
          fi

          echo "✅ Final Proxy Structure:"
          ls -R ./proxies
        shell: bash
      
      # Step 8: Upload Artifact
      - name: 📤 Uploadproxy artifacts
        uses: actions/upload-artifact@v4
        with: 
          name: artifacts
          path: proxies.zip

      # Step 9: Compare deployed Proxies on DEV
      - name: 🚢 Deploy Proxies to DEV
        id: deploy_dev
        run: |
          # Execute the Python script to check deployed proxies
          python3 central-repo/.github/scripts/deploy_on_dev.py \
            --artifact_path "./proxies" \
            --apigee_org "${{ vars.APIGEEX_DEV_ORG }}" \
            --environment "dev" \
            --access_token "${{ steps.non_prodAuth.outputs.access_token }}" \
            --output_file "dev_revision_comparison.json" \
            --dev_revisions_file "dev_deployment_revisions.json" \
            --success_file "success_deployments.txt" \
            --skipped_file "skipped_proxies.txt" \
            --error_file "deployment_errors.txt"

      # Step 10: compare Revisions
      - name: 🔖 Compare revisions on dev
        run: | 
            cat dev_revision_comparison.json
      
      # Step 11: Upload the deployment Revisions
      - name: 📤 UploadDeployment Revisions
        uses: actions/upload-artifact@v4
        with:
          name: deployment-revisions-dev
          path: dev_deployment_revisions.json
        
      - name: 🔖 Debug File Path
        run: |
          echo "Checking if notify.py exists:"
          echo "Full directory structure:"
          ls -R child-repo/
          ls -la
          tree

      # Step 12: Generate Product JSON and Deploy Products to DEV
      - name: 📄 Generate Product JSON and Deploy Products to DEV
        run: |
          # Set the path to the Excel file in the central repository
          EXCEL_FILE="central-repo/Apigee_Apps_Summary.xlsx"
          # Run the excel_to_json_multi_tabs.py script
          python3 central-repo/.github/scripts/excel_to_json_multi_tabs.py \
            --excel_file "$EXCEL_FILE" \
            --proxy_dir "./proxies" \
            --output_file "temp_products.json"

      # Step 13: Check the json file contents
      - name: 🔖 View Temp Json File
        run: |
          cat temp_products.json

      # Step 14: Call the deploy-products.sh script  
      - name: 🚢 Deploy products to DEV
        run: |
          bash central-repo/.github/scripts/deploy-products.sh "temp_products.json" dev ${{ vars.APIGEEX_DEV_ORG }} ${{ steps.non_prodAuth.outputs.access_token }}

     # Step 15: 📤 Uploadtemp_products.json as an artifact
      - name: 📤 UploadAPI Product Mapping JSON
        uses: actions/upload-artifact@v4
        with:
          name: api-product-mapping
          path: temp_products.json

      - name: 📡 Send New Relic Markers for DEV
        if: always()
        env:
          NEW_RELIC_API_KEY: ${{ secrets.NEW_RELIC_API_KEY }}
        run: |
          python3 central-repo/.github/scripts/post_newrelic_marker.py \
            --proxy_file success_deployments.txt \
            --environment DEV \
            --version "${{ inputs.pr_title }}" \
            --user "${{ inputs.commit_author_name }}" \
            --commit "${{ inputs.commit_sha }}"

      - name: upload the NewRelic summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: newrelic summary
          path: newrelic_marker_summary_dev.txt

     # Step 16: Send Email Notification for DEV via Python Script
      - name: 📢 Send Email Notification for DEV
        if: always() # Ensure this step runs regardless of success or failure
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: ${{ vars.EMAIL_RECIPIENTS }}
        run: |
          STATUS="success"
          if [[ -s deployment_errors.txt ]]; then
            STATUS="failure"
          fi

          ALL_RECIPIENTS="${{ inputs.commit_author_email }},${{ env.EMAIL_RECIPIENTS }}"
          echo "Sending email to: $ALL_RECIPIENTS"

          python central-repo/.github/scripts/notify.py \
            --status "$STATUS" \
            --environment "DEV" \
            --actor "${{ inputs.commit_author_name }}" \
            --pipeline_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            --recipients "$ALL_RECIPIENTS" \
            --success_file success_deployments.txt \
            --skipped_file skipped_proxies.txt \
            --error_file deployment_errors.txt \
            --created_products_file dev_created_products.txt \
            --updated_products_file dev_updated_products.txt

  # Job 3: Deploy Proxies to QA
  deploy-proxies-qa:
    name: 🚀 Deploy Proxies to QA
    needs: [ deploy-proxies-dev ]
    runs-on: ubuntu-latest
    environment:
      name: QA
    permissions:
      id-token: write
      contents: write
    steps:
      # Step 1: Authenticate with GCP for QA
      - name: 🔐 Authenticate with GCP
        id: qa_authNonprod
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      # Step 2: Checkout self repo
      - name: 📁 Checkout Full Repository
        uses: actions/checkout@v4
        with:
          path: central-repo
      
      # Checkout Child Repo
      - name: 📁 Checkout Code
        uses: actions/checkout@v4
        with:
          path: child-repo
          repository: ${{ github.event.inputs.repository }}
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.APIGEEX_CHILD_REPO_PAT }}
      
      # Step 3: Install Dependencies
      - name: 🚧 Install Required Tools
        run: |
          sudo apt-get update && sudo apt-get install -y jq zip python3 python3-pip
          pip3 install requests
          pip3 install pandas
          pip3 install openpyxl
    
     # Step 4:  📥 download dev deployment revisions
      - name:  📥 downloadDeployment Revisions
        uses: actions/download-artifact@v4
        with:
          name: deployment-revisions-dev
          path: dev_deployment_revisions

      # Step 5: Verify Downloaded Files
      - name: 🔖 Verify Downloaded Files
        run: |
            ls -R dev_deployment_revisions
            cat dev_deployment_revisions/dev_deployment_revisions.json


      # Step 6 : Deploy Proxies to QA Using Python Validation
      - name: 🚢 Deploy Proxies to QA
        run: |
            python3 central-repo/.github/scripts/deploy_on_qa.py \
            --dev_revisions_file dev_deployment_revisions/dev_deployment_revisions.json \
            --apigeex_org "${{ vars.APIGEEX_DEV_ORG }}" \
            --access_token "${{ steps.qa_authNonprod.outputs.access_token }}"

      # Step 7:  📥 download temp_products.json as an artifact
      - name:  📥 downloadAPI Product Mapping JSON
        uses: actions/download-artifact@v4
        with:
          name: api-product-mapping
          path: product-mappings

      #Step 8: Deploy Products to QA
      - name: 📝🚢 Generate Product JSON and Deploy Products to QA
        run: |

          # Call the deploy-products.sh script
          bash central-repo/.github/scripts/deploy-products.sh "product-mappings/temp_products.json" qa ${{ vars.APIGEEX_DEV_ORG }} ${{ steps.qa_authNonprod.outputs.access_token }}
        
      - name: 🔖 verify temp products file
        run: |
          cat product-mappings/temp_products.json
         
      # Step 9: Send Email Notification for QA via Python Script
      - name: 📢 Send Email Notification for QA
        if: always()
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: "${{ vars.EMAIL_RECIPIENTS }}"
        run: |
            # Determine the deployment status
            if [[ -s qa_success_deployments.txt ]]; then
              STATUS="Success"
            elif [[ -s qa_skipped_proxies.txt ]]; then
              STATUS="No New Deployments"
            else
              STATUS="No Activity"
            fi

            touch deployment_errors.txt
            cat deployment_errors.txt || echo "No errors found."

            ALL_RECIPIENTS="${{ inputs.commit_author_email }},${{ env.EMAIL_RECIPIENTS }}"
            echo "Sending email to: $ALL_RECIPIENTS"

            # Run the notify.py script
            python3 central-repo/.github/scripts/notify.py \
              --status "$STATUS" \
              --environment "QA" \
              --actor "${{ inputs.commit_author_name }}" \
              --pipeline_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
              --recipients "$ALL_RECIPIENTS" \
              --success_file qa_success_deployments.txt \
              --skipped_file qa_skipped_proxies.txt \
              --error_file qa_deployment_errors.txt \
              --created_products_file qa_created_products.txt \
              --updated_products_file qa_updated_products.txt \

      # Step 10: 📤 verify Deployed Revisions
      - name: 🔖 Verify QA Deployment Revisions File
        run: |
          cat qa_deployment_revisions.json

      # Step 11: 📤 Upload Revisions after deployment
      - name: 📤 UploadDeployment Revisions
        uses: actions/upload-artifact@v4
        with:
          name: deployment-revisions-qa
          path: qa_deployment_revisions.json
        
      - name: 📡 Send New Relic Markers for QA
        if: always()
        env:
          NEW_RELIC_API_KEY: ${{ secrets.NEW_RELIC_API_KEY }}
        run: |
          python3 central-repo/.github/scripts/post_newrelic_marker.py \
            --proxy_file qa_success_deployments.txt \
            --environment QA \
            --version "${{ inputs.pr_title }}" \
            --user "${{ inputs.commit_author_name }}" \
            --commit "${{ inputs.commit_sha }}"

      - name: upload the NewRelic summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: newrelic summary_qa
          path: newrelic_marker_summary_qa.txt
            
      # Step 12: Determine the status of the deployments for the workflow
      - name: 🩺 QA status
        id: qa_output
        run: |
          # Default status if no specific conditions are met
          STATUS="No_Activity"
      
          if [[ -s qa_success_deployments.txt ]]; then
            STATUS="Success"
            echo "status=success" >> "$GITHUB_OUTPUT"
            echo "QA Deployments were successful."
          elif [[ -s qa_skipped_proxies.txt ]]; then
            STATUS="Skipped"
            echo "status=skipped" >> "$GITHUB_OUTPUT"
            echo "No new QA Deployments were required - all already up to date."
          else
            echo "status=no_activity" >> "$GITHUB_OUTPUT"
            echo "No activity detected."
          fi
      
          echo "Final Deployment Status: $STATUS"

# Job 4: QA CheckPoint
  QA_CHECKPOINT:
    name:  🛂 QA Approval
    runs-on: ubuntu-latest
    needs: [ deploy-proxies-qa ]
    environment: "QA_CHECKPOINT"
    steps:
      - name: qa checkpoint
        run: |
          echo "Waiting for the QA APPROVAL"

  # # Job 6: Stage Deployment Approval
  # stage_deploy_approval:
  #   name: 🛂 Approve Stage Deployment
  #   runs-on: ubuntu-latest
  #   needs: [ QA_CHECKPOINT ]
  #   environment: "STAGE_Deploy_Approval"
  #   steps:
  #     - name: Wait for environment approval
  #       run: echo "Wait for APPROVAL"

  # Job 7: Deploy Proxies to STAGE Environment
  deploy-proxies-stage:
    name: 🚀 Deploy Proxies to STAGE
    needs: [ QA_CHECKPOINT ]
    runs-on: ubuntu-latest
    environment: "STAGE_Deploy_Approval"
    permissions:
      contents: write
      id-token: write
    outputs:
      deployment_status: ${{ steps.check_output.outputs.status }}
    steps:
      # Step 1: Checkout Code from the Triggering Repository
      - name: 📁 Checkout Code
        uses: actions/checkout@v4
        with:
          path: child-repo
          repository: ${{ github.event.inputs.repository }}
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.APIGEEX_CHILD_REPO_PAT }}

      # Step 2: Checkout self repo
      - name: 📁 Checkout Full Repository
        uses: actions/checkout@v4
        with:
          path: central-repo

      # Step 3: Authenticate with GCP
      - name: 🔐 Authenticate with GCP
        id: authStage
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_STAGE }}

      # Step 4: Install Dependencies
      - name: 🔨🚧 Install Required Tools
        run: |
          sudo apt-get update && sudo apt-get install -y jq zip python3 python3-pip
          pip3 install requests
          pip3 install pandas
          pip3 install openpyxl
      
      # Step 5:  📥 downloadLatest QA Deployment Mapping
      - name: Install GitHub CLI
        run: |
          sudo apt-get update
            sudo apt-get install -y gh
      # Step 6:  📥 download artifacts
      - name:  📥 📦️ downloadproxy artifacts
        uses: actions/download-artifact@v4
        with: 
          name: artifacts
          path: ./proxies_artifacts
      # Step 7:  📥 Unzip Artifacts and clean
      - name: Unzip and Cleanup Proxy Artifacts
        run: |
          ls -R ./proxies_artifacts
          unzip ./proxies_artifacts/proxies.zip -d ./proxies
          INNER_ZIP=$(find ./proxies -name "*.zip")

          if [[ -n "$INNER_ZIP" ]]; then
            echo "🔍 Found inner ZIP: $INNER_ZIP, extracting..."
            unzip "$INNER_ZIP" -d ./proxies
          else
            echo "⚠️ No inner ZIP found. Proceeding with existing files."
          fi

          echo "✅ Final Proxy Structure:"
          ls -R ./proxies
      # Step 8: debug
      - name: 🔍️ Debug Extracted Files Before Zipping
        run: |
          echo "🔍 Checking extracted proxy artifacts before zipping..."
          ls -R ./proxies || echo "⚠️ proxies folder is missing!"

      # Step 9: Deploy Proxies to Stage
      - name: 🚢 Deploy Proxies to Stage
        run: |

          python3 central-repo/.github/scripts/deploy_on_stage_prod.py \
            --repo_path "./proxies" \
            --apigeex_org "${{ vars.APIGEEX_ORG_STAGE }}" \
            --access_token "${{ steps.authStage.outputs.access_token }}" \
            --environment "stage" \
            --output_mapping_file "stage_deployment_revisions.json"
      
      # Step 10: 📥 downloadthe product mapping file
      - name:  📥 downloadproduct Mapping
        uses: actions/download-artifact@v4
        with:
          name: api-product-mapping
          path: product-mappings

      # Step 11: Deploy API Products to stage
      - name: 🚢 Deploy API Products to STAGE
        run: |
          cat product-mappings/temp_products.json
          # Location to store generated JSON
          TEMP_JSON_FILE="temp_products.json"
          # Deploy the products using the Bash script
          bash central-repo/.github/scripts/deploy-products.sh "product-mappings/temp_products.json" stage ${{ vars.APIGEEX_ORG_STAGE }} ${{ steps.authStage.outputs.access_token }}

      # Step 12: 📤 UploadStage Deployment Mapping
      - name: 📤 Upload Stage Deployment Mapping
        uses: actions/upload-artifact@v4
        with:
          name: deployment-revisions-stage
          path: stage_deployment_revisions.json

      # Step 12: Send Email Notification for Stage
      - name: 📢 Send Email Notification for stage
        if: always() # Ensure this step runs regardless of success or failure
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: ${{ vars.EMAIL_RECIPIENTS }}
        run: |
          STATUS="success"
          if [[ -s deployment_errors.txt ]]; then
            STATUS="failure"
          fi

          ALL_RECIPIENTS="${{ inputs.commit_author_email }},${{ env.EMAIL_RECIPIENTS }}"
          echo "Sending email to: $ALL_RECIPIENTS"

          python central-repo/.github/scripts/notify.py \
            --status "$STATUS" \
            --environment "STAGE" \
            --actor "${{ inputs.commit_author_name }}" \
            --pipeline_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            --recipients "$ALL_RECIPIENTS" \
            --success_file stage_success_deployments.txt \
            --skipped_file stage_skipped_proxies.txt \
            --error_file stage_deployment_errors.txt \
            --created_products_file stage_created_products.txt \
            --updated_products_file stage_updated_products.txt

      # Step 14: 🩺 Determine Deployment Status
      - name: Check Deployment Output
        id: check_output
        run: |
          if [[ -s success_deployments.txt ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
          elif [[ -s skipped_proxies.txt ]]; then
            echo "status=skipped" >> $GITHUB_OUTPUT
          else
            echo "status=no_activity" >> $GITHUB_OUTPUT
          fi

      - name: 📡 Send New Relic Markers for STAGE
        if: always()
        env:
          NEW_RELIC_API_KEY: ${{ secrets.NEW_RELIC_API_KEY }}
        run: |
          python3 central-repo/.github/scripts/post_newrelic_marker.py \
            --proxy_file stage_success_deployments.txt \
            --environment STAGE \
            --version "${{ inputs.pr_title }}" \
            --user "${{ inputs.commit_author_name }}" \
            --commit "${{ inputs.commit_sha }}"
      
      - name: upload the NewRelic summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: newrelic summary_stage
          path: newrelic_marker_summary_stage.txt

  # job 8: Prod Deployment Approval
  PROD_APPROVAL:
    name: 🛂 Prod QA Approval
    runs-on: ubuntu-latest
    needs: [ deploy-proxies-stage ]
    environment: "Prod-Qa-Approval"
    steps:
      - name: Wait for environment approval
        run: echo "APPROVE to proceed to Prod deployment."

  # job 9: Prod Approval
  PROD_DEPLOYMENT_APPROVAL:
    name: 🛂 Prod DBA Lead Approval
    runs-on: ubuntu-latest
    needs: [ PROD_APPROVAL ]
    environment: "Prod-DBA-Lead"
    steps:
      - name: Wait for environment approval
        run: echo "APPROVE to proceed to Prod deployment."

  # job 6: Deploy Proxies to PROD Environment
  deploy-proxies-prod:
    name: 🚀 Deploy Proxies to Prod
    needs: [ PROD_DEPLOYMENT_APPROVAL ]
    runs-on: ubuntu-latest
    environment: "PROD_Deploy_Approval"
    permissions:
      id-token: write
      contents: write
      actions: read
    steps:
      # Step 1: Authenticate with GCP for PROD
      - name: 🔐 Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_PROD }}

      # Step 2: Checkout Child Repo
      - name: 📁 Checkout Child Repo
        uses: actions/checkout@v4
        with:
          path: child-repo
          repository: ${{ github.event.inputs.repository }}
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.APIGEEX_CHILD_REPO_PAT }}

       # Step 3: Checkout self repo
      - name: 📁 Checkout Full Repository
        uses: actions/checkout@v4
        with:
          path: central-repo

      # Step 4: Install Dependencies
      - name: 🔨🚧 Install Required Tools
        run: |
          sudo apt-get update && sudo apt-get install -y jq zip python3 python3-pip
          pip3 install requests
          pip3 install pandas
          pip3 install openpyxl

      # Step 5: Dowload Artifacts
      - name:  📥 📦️ downloadproxy artifacts
        uses: actions/download-artifact@v4
        with: 
          name: artifacts
          path: ./proxies_artifacts
  
      - name: 🏗️ Unzip and Cleanup Proxy Artifacts
        run: |
          ls -R ./proxies_artifacts
          unzip ./proxies_artifacts/proxies.zip -d ./proxies
          INNER_ZIP=$(find ./proxies -name "*.zip")

          if [[ -n "$INNER_ZIP" ]]; then
            echo "🔍 Found inner ZIP: $INNER_ZIP, extracting..."
            unzip "$INNER_ZIP" -d ./proxies
          else
            echo "⚠️ No inner ZIP found. Proceeding with existing files."
          fi

          echo "✅ Final Proxy Structure:"
          ls -R ./proxies
          
      - name: Debug Extracted Files Before Zipping
        run: |
          echo "🔍 Checking extracted proxy artifacts before zipping..."
          ls -R ./proxies || echo "⚠️ proxies folder is missing!"

      # Step 5: Deploy Proxies to Prod
      - name: 🚢 Deploy Proxies to Prod
        run: |
          python3 central-repo/.github/scripts/deploy_on_stage_prod.py \
            --repo_path ./proxies \
            --apigeex_org "${{ vars.APIGEEX_ORG_PROD }}" \
            --access_token "${{ steps.auth.outputs.access_token }}" \
            --environment "prod" \
            --output_mapping_file "prod_deployment_revisions.json"

      # 📥 downloadthe product mapping file
      - name:  📥 downloadproduct Mapping
        uses: actions/download-artifact@v4
        with:
          name: api-product-mapping
          path: product-mappings

      # Step 6: 🚢 Deploy API Products to Prod
      - name: 🚢 Deploy API Products to PROD 
        run: |
          cat product-mappings/temp_products.json

          # Deploy the products using the Bash script
          bash central-repo/.github/scripts/deploy-products.sh "product-mappings/temp_products.json" prod ${{ vars.APIGEEX_ORG_PROD }} ${{ steps.auth.outputs.access_token }}

      # Step 7: 📤 UploadProd Deployment Mapping
      - name: 📤 UploadProd Deployment Mapping
        uses: actions/upload-artifact@v4
        with:
          name: deployment-revisions-prod
          path: prod_deployment_revisions.json

      # Step 8: Send Email Notification for Prod
      - name: 📢 Send Email Notification for Prod
        if: always() # Ensure this step runs regardless of success or failure
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: ${{ vars.EMAIL_RECIPIENTS }}
        run: |
          STATUS="success"
          if [[ -s deployment_errors.txt ]]; then
            STATUS="failure"
          fi

          ALL_RECIPIENTS="${{ inputs.commit_author_email }},${{ env.EMAIL_RECIPIENTS }}"
          echo "Sending email to: $ALL_RECIPIENTS"

          python3 central-repo/.github/scripts/notify.py \
            --status "$STATUS" \
            --environment "PROD" \
            --actor "${{ inputs.commit_author_name }}" \
            --pipeline_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            --recipients "$ALL_RECIPIENTS" \
            --success_file prod_success_deployments.txt \
            --skipped_file prod_skipped_proxies.txt \
            --error_file prod_deployment_errors.txt \
            --created_products_file prod_created_products.txt \
            --updated_products_file prod_updated_products.txt

      # Step 9: Determine Deployment Status
      - name: 🩺Check Deployment Output
        id: check_output
        run: |
          if [[ -s success_deployments.txt ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
          elif [[ -s skipped_proxies.txt ]]; then
            echo "status=skipped" >> $GITHUB_OUTPUT
          else
            echo "status=no_activity" >> $GITHUB_OUTPUT
          fi

      - name: 📡 Send New Relic Markers for PROD
        if: always()
        env:
          NEW_RELIC_API_KEY: ${{ secrets.NEW_RELIC_API_KEY }}
        run: |
          python3 central-repo/.github/scripts/post_newrelic_marker.py \
            --proxy_file prod_success_deployments.txt \
            --environment PROD \
            --version "${{ inputs.pr_title }}" \
            --user "${{ inputs.commit_author_name }}" \
            --commit "${{ inputs.commit_sha }}"

      - name: upload the NewRelic summary
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: newrelic summary_prod
          path: newrelic_marker_summary_prod.txt

# job 10: Prod Approval
  Release_APPROVAL:
    name: 🛂 SRE Relase and Merge Code
    runs-on: ubuntu-latest
    needs: [ deploy-proxies-prod ]
    environment: "SRE Release and Merge"
    steps:
      - name: Wait for environment approval
        run: echo "APPROVE to proceed Release."

  # Job 11
  release-revisions:
    name: 🏷️ Revions and Release
    needs: [ Release_APPROVAL ]
    runs-on: ubuntu-latest
    steps:
      - name:  📥 downloadDEV Deployment Revisions
        uses: actions/download-artifact@v4
        with:
          name: deployment-revisions-dev
          path: revisions
      
      - name:  📥 downloadQA Deployment Revisions
        uses: actions/download-artifact@v4
        with:
          name: deployment-revisions-qa
          path: revisions

      - name:  📥 downloadDEV Deployment Revisions
        uses: actions/download-artifact@v4
        with:
          name: deployment-revisions-stage
          path: revisions
      
      - name:  📥 downloadQA Deployment Revisions
        uses: actions/download-artifact@v4
        with:
          name: deployment-revisions-prod
          path: revisions

      - name:  📥 downloadtemp json file
        uses: actions/download-artifact@v4
        with:
          name: api-product-mapping
          path: revisions
      
      - name: List Downloaded Revision Files
        run: ls -R revisions

      - name: Extract Repository Name
        run: |
          REPO_NAME=$(echo "${{ github.repository }}" | awk -F '/' '{print $2}')
          echo "REPO_NAME=$REPO_NAME" >> $GITHUB_ENV
      
      - name: Create GitHub Release
        uses: ncipollo/release-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: "release-non-prod-${{ inputs.repository }}-${{ inputs.branch }}-${{ inputs.pr_number }}-${{ github.run_number }}"
          name: "Apigee Deployment - ${{ github.run_id }}"
          body: |
            ## Apigee Deployment Summary
            - **Repo:** `${{ inputs.repository }}`
            - **Branch:** `${{ inputs.branch }}`
            - **Commit SHA:** `${{ inputs.commit_sha }}`
            - **QA Approved:** ✅
            ### 📌 Deployment Artifacts Revisions
            - **DEV Revisions:** Stored in `dev_deployment_revisions.json`
            - **QA Revisions:** Stored in `qa_deployment_revisions.json`
            - **STAGE Revisions:** Stored in `stage_deployment_revisions.json`
            - **PROD Revisions:** Stored in `prod_deployment_revisions.json`
            - **Rollback Supported:** ✅ Use this tag if rollback is needed.
            ---
            *Generated automatically by GitHub Actions.*
          artifacts: |
            revisions/dev_deployment_revisions.json
            revisions/qa_deployment_revisions.json
            revisions/stage_deployment_revisions.json
            revisions/prod_deployment_revisions.json
          draft: false
          prerelease: false

      # Step 3: Trigger Auto-Approval & Merge PR from Feature → Dev in Child Repo
      - name: Approve & Merge PR from Feature → Dev in Child Repo
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: pr-auto-approve-merge.yml
          repo: ${{ inputs.repository }}
          token: ${{ secrets.APIGEEX_CHILD_REPO_PAT }}
          inputs: |
            {
              "pr_number": "${{ inputs.pr_number }}",
              "repository": "${{ inputs.repository }}",
              "merge_target": "dev"
            }