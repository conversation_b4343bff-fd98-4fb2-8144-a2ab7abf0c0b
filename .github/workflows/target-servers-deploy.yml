name: 📡 Deploy Target Servers to Apigee

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy (dev, qa, stage, prod)"
        required: true
        type: choice
        options:
          - dev
          - qa
          - stage
          - prod

jobs:
  deploy-target-servers:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: 🛠️ Checkout Repository
        uses: actions/checkout@v4

      - name: 🌍 Set Environment Variables
        id: set_env
        run: |
          TARGET_ENV="${{ github.event.inputs.environment }}"
          
          if [[ -z "$TARGET_ENV" ]]; then
            echo " No environment selected. Exiting."
            exit 1
          fi

          # Assign the correct Apigee organization
          case "$TARGET_ENV" in
            dev|qa) echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV }}" >> $GITHUB_ENV ;;
            stage)  echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_ENV ;;
            prod)   echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_ENV ;;
          esac

          echo "🚀 Deploying Target Servers to: $TARGET_ENV in Org: $APIGEE_ORG"
          echo "TARGET_ENV=$TARGET_ENV" >> $GITHUB_ENV

      # 🟢 Authenticate with GCP for each environment
      - name: 🔑 Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars[format('APIGEEX_CICD_SA_{0}', env.TARGET_ENV)] }}

      - name: 🚀 Set Access Token
        run: echo "ACCESS_TOKEN=${{ steps.auth.outputs.access_token }}" >> $GITHUB_ENV

      - name: 📄 Clear Previous Logs
        run: |
          echo "Clearing previous logs..."
          > target_servers_success.log
          > target_servers_failed.log

      - name: 📡 Deploy Target Servers
        run: |
          echo "Deploying target servers for environment: $TARGET_ENV in Apigee Org: $APIGEE_ORG"
          python3 .github/scripts/deploy_target_servers.py \
            --config_dir "target-servers-config/$TARGET_ENV" \
            --apigee_org "$APIGEE_ORG" \
            --environment "$TARGET_ENV" \
            --access_token "$ACCESS_TOKEN"

      - name: 📤 Upload Deployment Logs
        uses: actions/upload-artifact@v4
        with:
          name: target-server-deployment-logs-${{ env.TARGET_ENV }}
          path: |
            target_servers_success.log
            target_servers_failed.log

      - name: 📧 Send Notification (Optional)
        if: always()
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: ${{ vars.EMAIL_RECIPIENTS }}
        run: |
          STATUS="success"
          if [[ -s target_servers_failed.log ]]; then
            STATUS="failure"
          fi

          python3 .github/scripts/notify.py \
            -s "$STATUS" \
            -e "$TARGET_ENV" \
            -a "${{ github.actor }}" \
            -p "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            -r "$EMAIL_RECIPIENTS" \
            --success_file target_servers_success.log \
            --error_file target_servers_failed.log
