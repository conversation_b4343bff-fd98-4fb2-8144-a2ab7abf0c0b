name: FAILOVER KVM SWITCH
run-name: KVM SWITCH triggered by ${{ github.actor }} on ${{ inputs.environment }} to ${{ inputs.failover_state }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select Apigee Environment'
        required: true
        type: choice
        options:
          - dev
          - qa
          - stage
          - prod
      failover_state:
        description: 'Failover State (Primary or Secondary)'
        required: true
        type: choice
        options:
          - primary
          - secondary

jobs:
  update-kvm:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Print Inputs for Debugging
        run: |
          echo "Environment: ${{ inputs.environment }}"
          echo "Failover State: ${{ inputs.failover_state }}"

      - name: Set Service Account and Org ID Based on Environment
        run: |
          if [[ "${{ inputs.environment }}" == "dev" || "${{ inputs.environment }}" == "qa" ]]; then
            echo "SERVICE_ACCOUNT=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_ENV
            echo "APIGEE_ORG=cei-tdgkxj26" >> $GITHUB_ENV
          elif [[ "${{ inputs.environment }}" == "stage" ]]; then
            echo "SERVICE_ACCOUNT=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_ENV
            echo "APIGEE_ORG=cei-lfj1ov9u" >> $GITHUB_ENV
          elif [[ "${{ inputs.environment }}" == "prod" ]]; then
            echo "SERVICE_ACCOUNT=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_ENV
            echo "APIGEE_ORG=cei-vbxef9zh" >> $GITHUB_ENV
          fi

      - name: Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: 'access_token'
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ env.SERVICE_ACCOUNT }}

      - name: Update KVM Entry
        env:
          ACCESS_TOKEN: ${{ steps.auth.outputs.access_token }}
        run: |
          API_URL="https://apigee.googleapis.com/v1/organizations/${{ env.APIGEE_ORG }}/environments/${{ inputs.environment }}/keyvaluemaps/KVM-TargetServerSwitch/entries/false"

          DATA='{"name": "Target-server", "value": "${{ inputs.failover_state }}"}'

          RESPONSE=$(curl -s -o response.json -w "%{http_code}" -X PUT "$API_URL" \
            -H "Authorization: Bearer $ACCESS_TOKEN" \
            -H "Content-Type: application/json" \
            -d "$DATA")

          if [ "$RESPONSE" -eq 200 ]; then
            echo "KVM updated successfully to ${{ inputs.failover_state }}"
          else
            echo "Failed to update KVM. HTTP Status: $RESPONSE"
            cat response.json
            exit 1
          fi