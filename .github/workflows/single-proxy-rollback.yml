name: 🔄 Apigee Single Proxy Rollback
run-name: ${{ inputs.proxy_name }} rolling back to revision ${{ inputs.rollback_revision }}
on:
  workflow_dispatch:
    inputs:
      proxy_name:
        description: 'Proxy to rollback (e.g., hello-world)'
        required: true
      environment:
        description: 'Environment to rollback (dev/qa/stage/prod)'
        required: true
        type: choice
        options:
          - dev
          - qa
          - stage
          - prod
      rollback_revision:
        description: 'Revision to rollback to'
        required: true

jobs:
  rollback-proxy:
    name: 🔄 Rollback ${{ inputs.proxy_name }} in ${{ inputs.environment }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
      actions: read

    steps:
      - name: 🛠️ Checkout Repository
        uses: actions/checkout@v4

      - name: 🌍 Set Apigee Environment Variables
        run: |
          case "${{ github.event.inputs.environment }}" in
            dev)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_ENV
              ;;
            qa)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_NONPROD}}" >> $GITHUB_ENV
              ;;
            stage)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_ENV
              ;;
            prod)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_ENV
              ;;
            *)
              echo "❌ Invalid environment provided. Exiting."
              exit 1
              ;;
          esac
          echo "✅ Environment Variables Set: APIGEE_ORG=$APIGEE_ORG, APIGEE_SA=$APIGEE_SA"

      - name: 🔑 Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ env.APIGEE_SA }}

      - name: 🚀 Validate and Rollback Proxy in Apigee
        run: |
          PROXY_NAME="${{ github.event.inputs.proxy_name }}"
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          ROLLBACK_REVISION="${{ github.event.inputs.rollback_revision }}"

          echo "🔍 Checking currently deployed revision for '$PROXY_NAME' in '$ENVIRONMENT'..."

          # Fetch currently deployed revision
          CURRENT_REVISION=$(curl -s -H "Authorization: Bearer ${{ steps.auth.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ env.APIGEE_ORG }}/environments/$ENVIRONMENT/apis/$PROXY_NAME/deployments" | \
            jq -r '.deployments[].revision' | sort -nr | head -1)

          if [ -z "$CURRENT_REVISION" ] || [ "$CURRENT_REVISION" == "null" ]; then
            echo "❌ Failed to fetch current revision for $PROXY_NAME. Exiting..."
            exit 1
          fi

          echo "✅ Current revision for $PROXY_NAME: $CURRENT_REVISION"

          # Validate rollback revision
          if [ "$ROLLBACK_REVISION" -ge "$CURRENT_REVISION" ]; then
            echo "❌ Rollback revision ($ROLLBACK_REVISION) must be lower than the current revision ($CURRENT_REVISION). Exiting..."
            exit 1
          fi

          echo "🔄 Rolling back proxy '$PROXY_NAME' in '$ENVIRONMENT' to revision $ROLLBACK_REVISION..."

          # Rollback API Call
          ROLLBACK_RESPONSE=$(curl -s -X POST -H "Authorization: Bearer ${{ steps.auth.outputs.access_token }}" \
            -H "Content-Type: application/json" \
            "https://apigee.googleapis.com/v1/organizations/${{ env.APIGEE_ORG }}/environments/$ENVIRONMENT/apis/$PROXY_NAME/revisions/$ROLLBACK_REVISION/deployments?override=true")

          if echo "$ROLLBACK_RESPONSE" | jq -e '.error' > /dev/null; then
            echo "❌ Rollback failed for $PROXY_NAME revision $ROLLBACK_REVISION. Response: $ROLLBACK_RESPONSE"
            exit 1
          else
            echo "✅ Successfully rolled back $PROXY_NAME to revision $ROLLBACK_REVISION"
            echo "$PROXY_NAME rolled back to revision $ROLLBACK_REVISION" > rolled_back_proxies.txt
          fi

      - name: 📧 Send Rollback Notification
        if: always()
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          EMAIL_RECIPIENTS: ${{ vars.EMAIL_RECIPIENTS }}
        run: |
          python3 .github/scripts/rollback_notify.py \
            --status "success" \
            --environment "${{ github.event.inputs.environment }}" \
            --actor "${{ github.actor }}" \
            --run_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            --recipients "$EMAIL_RECIPIENTS" \
            --rolled_back_proxies rolled_back_proxies.txt
