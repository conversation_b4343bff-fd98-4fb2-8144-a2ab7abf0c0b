name: Promote Existing Shared Flows from Stage → Prod
run-name: ${{ github.actor }} promoted shared flows to PROD for ${{ inputs.sharedflows }}

on:
  workflow_dispatch:
    inputs:
      sharedflows:
        description: "Comma-separated list of shared flow names (e.g., SF-Logging,SF-ErrorHandler)"
        required: true

jobs:
  promote-to-prod:
    name: Promote Shared Flows from Stage to Prod
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: Authenticate with GCP (Stage)
        id: auth_stage
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_STAGE }}

      - name: Authenticate with GCP (Prod)
        id: auth_prod
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_PROD }}

      - name: Download Shared Flows from Stage
        run: |
          mkdir -p sharedflows
          IFS=',' read -ra SHARED_FLOWS <<< "${{ inputs.sharedflows }}"

          for FLOW_NAME in "${SHARED_FLOWS[@]}"; do
            echo "Fetching latest deployed revision for $FLOW_NAME from STAGE..."

            # Get latest deployed revision in Stage
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/environments/stage/sharedflows/$FLOW_NAME/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            if [ -z "$LATEST_REV" ] || [ "$LATEST_REV" == "null" ]; then
              echo "No active deployed revision found for $FLOW_NAME in STAGE. Skipping..."
              continue
            fi

            echo " Found active revision $LATEST_REV for $FLOW_NAME in STAGE. Downloading bundle..."

            # Download the shared flow ZIP from Stage org
            curl -s -L -o "sharedflows/${FLOW_NAME}.zip" \
              -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/sharedflows/$FLOW_NAME/revisions/$LATEST_REV?format=bundle"

            if [ ! -f "sharedflows/${FLOW_NAME}.zip" ]; then
              echo "Failed to download $FLOW_NAME from STAGE."
              continue
            fi

            echo "Successfully downloaded $FLOW_NAME revision $LATEST_REV."
          done

      - name: Upload and Deploy to Prod
        run: |
          for ZIP_FILE in sharedflows/*.zip; do
            FLOW_NAME=$(basename "$ZIP_FILE" .zip)
            echo "Uploading $FLOW_NAME to PROD..."

            # Upload to PROD
            curl -s -X POST \
              -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              -F "file=@$ZIP_FILE" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/sharedflows?action=import&name=$FLOW_NAME"

            # Get new revision
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/sharedflows/$FLOW_NAME/revisions" | jq -r 'max')

            # Deploy new revision
            echo "🚀 Deploying $FLOW_NAME revision $LATEST_REV to PROD..."
            curl -s -X POST \
              -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/environments/prod/sharedflows/$FLOW_NAME/revisions/$LATEST_REV/deployments?override=true"

            echo "Successfully deployed $FLOW_NAME revision $LATEST_REV to PROD."
          done
