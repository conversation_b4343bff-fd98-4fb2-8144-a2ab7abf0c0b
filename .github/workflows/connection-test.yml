name: Test Apigee Org Connectivity

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to test: dev, qa, stage, or prod"
        required: true
        type: choice
        options: [dev, qa, stage, prod]

jobs:
  test-connection:
    name: Test Apigee Connection
    runs-on: ubuntu-latest
    permissions:
        id-token: write
        contents: write
    env:
      APIGEE_ENV: ${{ github.event.inputs.environment }}

    steps:
      - name: Set Org and SA based on Environment
        id: config
        run: |
          if [[ "$APIGEE_ENV" == "dev" || "$APIGEE_ENV" == "qa" ]]; then
            echo "ORG_ID=cei-tdgkxj26" >> $GITHUB_ENV
            echo "SERVICE_ACCOUNT=<EMAIL>" >> $GITHUB_ENV
            echo "TARGET_ENV=$APIGEE_ENV" >> $GITHUB_ENV
          elif [[ "$APIGEE_ENV" == "stage" ]]; then
            echo "ORG_ID=cei-lfj1ov9u" >> $GITHUB_ENV
            echo "SERVICE_ACCOUNT=<EMAIL>" >> $GITHUB_ENV
            echo "TARGET_ENV=stage" >> $GITHUB_ENV
          elif [[ "$APIGEE_ENV" == "prod" ]]; then
            echo "ORG_ID=cei-vbxef9zh" >> $GITHUB_ENV
            echo "SERVICE_ACCOUNT=<EMAIL>" >> $GITHUB_ENV
            echo "TARGET_ENV=prod" >> $GITHUB_ENV
          else
            echo "Invalid environment: $APIGEE_ENV"
            exit 1
          fi

      - name: Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ env.SERVICE_ACCOUNT }}

      - name: Test Apigee Org Connectivity
        run: |
          echo "Testing Apigee Org: $ORG_ID for environment: $TARGET_ENV"
          curl -s -H "Authorization: Bearer ${{ steps.auth.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/$ORG_ID/environments" \
            | jq .

