name: 🔑 Apigee KVM Management 
run-name: 🛠️ KVM Action ${{inputs.kvm_action}} on ${{ inputs.environment }} by ${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      proxies:
        description: "Comma-separated list of proxy names (optional, leave blank for environment-level KVMs)"
        required: false
      kvm_action:
        description: "Action to perform (view/create/update/delete Deleting specific keys (if kvm_keys is provided)
                      Deleting the entire KVM (if kvm_keys is empty))"
        required: true
        type: choice
        options:
          - create
          - update
          - delete
          - view
      kvm_name:
        description: "KVM name (optional for 'view')"
        required: false
      environment:
        description: "Target environment"
        required: true
        type: choice
        options:
          - dev
          - qa
          - stage
          - prod
      kvm_keys:
        description: "Comma-separated list of keys"
        required: false
      kvm_values:
        description: "Comma-separated list of values"
        required: false

jobs:
  kvm-management:
    name: 🔄 Manage KVMs in Apigee
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment}}
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🌍 Set Apigee Environment Variables
        run: |
          echo "🔍 Setting Apigee Environment Variables..."
          case "${{ inputs.environment }}" in
            dev)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_ENV
              ;;
            qa)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_ENV
              ;;
            stage)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_ENV
              ;;
            prod)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_ENV
              echo "APIGEE_SA=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_ENV
              ;;
            *)
              echo "Invalid environment provided. Exiting..."
              exit 1
              ;;
          esac

      - name: 🔑 Authenticate with Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ env.APIGEE_SA }}

      - name: 🔄 Perform KVM Operation
        run: |
          set -x

          KVM_ACTION="${{ inputs.kvm_action }}"
          KVM_NAME="${{ inputs.kvm_name }}"
          ENVIRONMENT="${{ inputs.environment }}"
          KVM_KEYS="${{ inputs.kvm_keys }}"
          KVM_VALUES="${{ inputs.kvm_values }}"
          API_URL="https://apigee.googleapis.com/v1/organizations/$APIGEE_ORG/environments/$ENVIRONMENT/keyvaluemaps"

          echo "📝 KVM Action: $KVM_ACTION"
          echo "🔍 KVM Name: $KVM_NAME"
          echo "📍 Environment: $ENVIRONMENT"
          echo "🔑 Keys: $KVM_KEYS"
          echo "📥 Values: $KVM_VALUES"

          ACCESS_TOKEN="${{ steps.auth.outputs.access_token }}"

          if [[ "$KVM_ACTION" == "view" ]]; then
            mkdir -p kvm-dump

            if [[ -z "$KVM_NAME" ]]; then
              echo "Fetching all KVMs and writing their entries to files..."
              KVM_LIST=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" "$API_URL" | jq -r '.[]')

              if [[ -z "$KVM_LIST" ]]; then
                echo "No KVMs found in $ENVIRONMENT."
                exit 0
              fi

              for KVM in $KVM_LIST; do
                echo "Saving entries for KVM: $KVM"
                curl -s -H "Authorization: Bearer $ACCESS_TOKEN" "$API_URL/$KVM/entries" | jq '.' > "kvm-dump/${KVM}-entries.json"
              done
            else
              echo "Saving entries for KVM: $KVM_NAME"
              curl -s -H "Authorization: Bearer $ACCESS_TOKEN" "$API_URL/$KVM_NAME/entries" | jq '.' > "kvm-dump/${KVM_NAME}-entries.json"
            fi

            exit 0
          fi

          # Check if KVM exists
          echo " Checking if KVM '$KVM_NAME' exists..."
          KVM_EXISTENCE=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: Bearer $ACCESS_TOKEN" "$API_URL/$KVM_NAME/entries")

          if [ "$KVM_EXISTENCE" == "404" ]; then
            echo " KVM '$KVM_NAME' does not exist. Creating it..."
            CREATE_RESPONSE=$(curl -s -X POST \
              -H "Authorization: Bearer $ACCESS_TOKEN" \
              -H "Content-Type: application/json" \
              --data-raw "{\"name\": \"$KVM_NAME\"}" \
              "$API_URL")

            echo " API Response (Create KVM): $CREATE_RESPONSE"
            if echo "$CREATE_RESPONSE" | jq -e '.error' > /dev/null; then
              echo "ERROR: Failed to create KVM."
              exit 1
            else
              echo "✅ KVM '$KVM_NAME' successfully created."
            fi
          else
            echo "✅ KVM '$KVM_NAME' already exists."
          fi

          if [[ "$KVM_ACTION" == "create" || "$KVM_ACTION" == "update" ]]; then
            if [ -z "$KVM_KEYS" ] || [ -z "$KVM_VALUES" ]; then
              echo "Missing KVM keys or values."
              exit 1
            fi

            IFS=',' read -ra KEYS <<< "$KVM_KEYS"
            IFS=',' read -ra VALUES <<< "$KVM_VALUES"

            if [ "${#KEYS[@]}" -ne "${#VALUES[@]}" ]; then
              echo "Number of keys and values do not match."
              exit 1
            fi

            for i in "${!KEYS[@]}"; do
              ENTRY_PAYLOAD="{\"name\": \"${KEYS[i]}\", \"value\": \"${VALUES[i]}\"}"
              echo "🔄 Adding Entry: ${KEYS[i]}=${VALUES[i]}"
              RESPONSE=$(curl -s -X POST \
                -H "Authorization: Bearer $ACCESS_TOKEN" \
                -H "Content-Type: application/json" \
                --data-raw "$ENTRY_PAYLOAD" \
                "$API_URL/$KVM_NAME/entries")

              echo " API Response: $RESPONSE"
              if echo "$RESPONSE" | jq -e '.error' > /dev/null; then
                echo "ERROR: Failed to add/update entry: ${KEYS[i]}"
                exit 1
              else
                echo "✅ Successfully added/updated entry: ${KEYS[i]}"
              fi
            done
          fi
          # KVM Delete Action 
          if [[ "$KVM_ACTION" == "delete" ]]; then
            if [ -z "$KVM_KEYS" ]; then
              echo "No keys provided. Deleting the entire KVM: $KVM_NAME..."
              DELETE_RESPONSE=$(curl -s -X DELETE \
                -H "Authorization: Bearer $ACCESS_TOKEN" \
                "$API_URL/$KVM_NAME")

              echo " API Response (Delete KVM): $DELETE_RESPONSE"
              if echo "$DELETE_RESPONSE" | jq -e '.error' > /dev/null; then
                echo "ERROR: Failed to delete KVM."
                exit 1
              else
                echo "✅ KVM '$KVM_NAME' deleted successfully."
              fi
            else
              IFS=',' read -ra DELETE_KEYS <<< "$KVM_KEYS"
              for KEY in "${DELETE_KEYS[@]}"; do
                echo "🗑️ Deleting KVM entry: $KEY from $KVM_NAME..."
                DELETE_ENTRY_RESPONSE=$(curl -s -X DELETE \
                  -H "Authorization: Bearer $ACCESS_TOKEN" \
                  "$API_URL/$KVM_NAME/entries/$KEY")

                echo " API Response: $DELETE_ENTRY_RESPONSE"
                if echo "$DELETE_ENTRY_RESPONSE" | jq -e '.error' > /dev/null; then
                  echo "ERROR: Failed to delete key: $KEY"
                  exit 1
                else
                  echo "✅ Successfully deleted key: $KEY"
                fi
              done
            fi
          fi
      
      - name: 📦 Upload KVM Entries as Artifact
        if: ${{ inputs.kvm_action == 'view' }}
        uses: actions/upload-artifact@v4
        with:
          name: kvm-entry-dump-${{ inputs.environment }}
          path: kvm-dump/
          retention-days: 2
        