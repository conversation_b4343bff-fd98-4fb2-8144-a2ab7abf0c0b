name: Create New Credentials for Apigee Apps

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (dev, qa, stage, prod)'
        required: true
        type: choice
        options:
          - dev
          - qa
          - stage
          - prod
      app_names:
        description: 'Comma-separated list of Apigee App Names'
        required: true
      delete_old_keys:
        description: 'Delete existing consumer keys?'
        required: true
        type: choice
        options:
          - "yes"
          - "no"

jobs:
  lead-approval:
    name:  🛂 Lead Approval
    runs-on: ubuntu-latest
    environment: "PROD"
    steps:
      - name: Approval
        run: |
          echo "Waiting for Approval"
    
  create-app-credentials:
    runs-on: ubuntu-latest
    needs: lead-approval
    permissions:
      id-token: write
      contents: read

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Install Python and Required Tools
        run: |
          sudo apt-get update && sudo apt-get install -y python3 python3-pip jq
          pip3 install requests pandas openpyxl

      - name: Set Environment Variables
        id: envmap
        run: |
          if [[ "${{ inputs.environment }}" == "dev" || "${{ inputs.environment }}" == "qa" ]]; then
            echo "org=${{ vars.APIGEEX_DEV_ORG }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.environment }}" == "stage" ]]; then
            echo "org=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
          elif [[ "${{ inputs.environment }}" == "prod" ]]; then
            echo "org=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_OUTPUT
            echo "sa=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_OUTPUT
            echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
          else
            echo "Invalid environment" && exit 1
          fi

      - name: Authenticate to GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ steps.envmap.outputs.wif }}
          service_account: ${{ steps.envmap.outputs.sa }}

      - name: Generate Product Mapping from Excel
        run: |
          python3 .github/scripts/excel-json-mapping.py \
            --excel_file "central-repo/Apigee_Apps_Summary.xlsx" \
            --output_file "temp_products.json"

      - name: Create Credentials and Map Products
        run: |
          python3 .github/scripts/add_app_credential.py \
            --input_json_file temp_products.json \
            --apigee_org "${{ steps.envmap.outputs.org }}" \
            --developer_email "${{ steps.envmap.outputs.sa }}" \
            --access_token "${{ steps.auth.outputs.access_token }}" \
            --app_names "${{ inputs.app_names }}" \
            --delete_old_keys "${{ inputs.delete_old_keys }}"
