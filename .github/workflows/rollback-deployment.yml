name: 🔄 Apigee Deployment Rollback
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback (qa/stage/prod)'
        required: true
        default: 'stage'
        type: choice
        options:
          - qa
          - stage
          - prod
      release_tag:
        description: 'GitHub Release Tag for rollback'
        required: true

jobs:
  rollback:
    name: 🛠️ Rollback Deployment
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 🌍 Set Environment Variables Based on Environment
        run: |
          case "${{ github.event.inputs.environment }}" in
            qa)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_DEV}}" >> $GITHUB_ENV
              echo "APIGEEX_SA=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_ENV
              ;;
            stage)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_STAGE }}" >> $GITHUB_ENV
              echo "APIGEEX_SA=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_ENV
              ;;
            prod)
              echo "APIGEE_ORG=${{ vars.APIGEEX_ORG_PROD }}" >> $GITHUB_ENV
              echo "APIGEEX_SA=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_ENV
              ;;
            *)
              echo "Invalid environment. Exiting..."
              exit 1
              ;;
          esac
          echo "Environment Variables Set: APIGEE_ORG=$APIGEE_ORG, APIGEEX_SA=$APIGEEX_SA"

      - name: 🔑 Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ env.APIGEEX_SA }}

      - name: 🚀 Download Deployment Mapping from Release
        run: |
          REPO="cox-cei/apigeex-CICD-Central"
          TOKEN="${{ secrets.GITHUB_TOKEN }}"
          RELEASE_TAG="${{ github.event.inputs.release_tag }}"
          ENV="${{ github.event.inputs.environment }}"
      
          case "$ENV" in
            qa) DEPLOYMENT_FILE="qa_deployment_revisions.json" ;;
            stage) DEPLOYMENT_FILE="stage_deployment_revisions.json" ;;
            prod) DEPLOYMENT_FILE="prod_deployment_revisions.json" ;;
          esac
      
          echo "Fetching rollback mappings from release: $RELEASE_TAG"
      
          # Get the Release ID
          RELEASE_ID=$(curl -s -L \
            -H "Authorization: Bearer $TOKEN" \
            -H "Accept: application/json" \
            "https://api.github.com/repos/$REPO/releases/tags/$RELEASE_TAG" | jq -r '.id')
      
          if [[ -z "$RELEASE_ID" || "$RELEASE_ID" == "null" ]]; then
            echo " Failed to find release with tag $RELEASE_TAG. Exiting..."
            exit 1
          fi
      
          echo " Found Release ID: $RELEASE_ID"
      
          # Download the file
          echo " Downloading $DEPLOYMENT_FILE from release..."
          gh release download "$RELEASE_TAG" --repo "$REPO" --pattern "$DEPLOYMENT_FILE" --dir .
      
          # Validate download
          if [[ ! -f "$DEPLOYMENT_FILE" ]]; then
            echo " Failed to download $DEPLOYMENT_FILE. Exiting..."
            exit 1
          fi
      
          echo " Successfully downloaded $DEPLOYMENT_FILE:"
          cat "$DEPLOYMENT_FILE"
      
          # Export the environment variable for the next step
          echo "DEPLOYMENT_FILE=$DEPLOYMENT_FILE" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 🚀 Execute Rollback Deployment
        run: |
          if [[ ! -f "$DEPLOYMENT_FILE" ]]; then
            echo "❌ Deployment file missing: $DEPLOYMENT_FILE. Exiting..."
            exit 1
          fi
          
          echo "✅ Running rollback script for environment: ${{ github.event.inputs.environment }}"
          python3 .github/scripts/rollback_deploy.py \
            --deployment_file "$DEPLOYMENT_FILE" \
            --apigee_org "${{ env.APIGEE_ORG }}" \
            --access_token "${{ steps.auth.outputs.access_token }}" \
            --environment "${{ github.event.inputs.environment }}" \
            --output_file "rollback_summary.txt"

      # Step: Send Email Notification for Rollback
      - name: 📢 Send Email Notification for Rollback
        if: always() # Ensure this step runs regardless of success or failure
        env:
          SENDGRID_API_KEY: ${{ secrets.SENDGRID_API_KEY }}
          ROLLBACK_RECIPIENTS: ${{ vars.ROLLBACK_RECIPIENTS }}
        run: |
          STATUS="success"
          if [[ -s rollback_errors.txt ]]; then
            STATUS="failure"
          fi
        
          RECIPIENTS="${{ env.ROLLBACK_RECIPIENTS }}"
          echo "Sending email to: $ROLLBACK_RECIPIENTS"
        
          python3 .github/scripts/rollback_notify.py \
            --status "$STATUS" \
            --environment "${{ github.event.inputs.environment }}" \
            --actor "${{ github.actor }}" \
            --run_url "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}" \
            --recipients "$RECIPIENTS" \
            --rolled_back_proxies rollback_summary.txt \


