name: 🔀 Shared Flow Deployment Pipeline (DEV → QA → Stage → Prod)
run-name: ${{ github.actor }} triggered shared flow deployment for ${{inputs.sharedflows}}
on:
  workflow_dispatch:
    inputs:
      sharedflows:
        description: "Comma-separated list of shared flow names (e.g., SF-Logging,SF-Error<PERSON><PERSON>ler)"
        required: true

jobs:
  # Step 1: Download from DEV
  download-from-dev:
    name: 📥 Download Shared Flows from DEV
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (DEV)
        id: auth_dev
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      - name: 📥 Download Shared Flows from DEV
        run: |
          IFS=',' read -ra SHARED_FLOWS <<< "${{ inputs.sharedflows }}"
          mkdir -p sharedflows

          for FLOW_NAME in "${SHARED_FLOWS[@]}"; do
            echo "🔍 Fetching latest deployed revision for $FLOW_NAME in DEV..."
            
            # Fetch latest active revision from DEV
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_dev.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/dev/sharedflows/$FLOW_NAME/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            if [ -z "$LATEST_REV" ] || [ "$LATEST_REV" == "null" ]; then
              echo "❌ No active deployed revision found for $FLOW_NAME in DEV. Skipping..."
              continue
            fi

            echo "✅ Found active revision $LATEST_REV for $FLOW_NAME."

            # Download the shared flow ZIP
            curl -s -L -o "sharedflows/${FLOW_NAME}.zip" \
              -H "Authorization: Bearer ${{ steps.auth_dev.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/sharedflows/$FLOW_NAME/revisions/$LATEST_REV?format=bundle"

            if [ ! -f "sharedflows/${FLOW_NAME}.zip" ]; then
              echo "❌ Failed to download $FLOW_NAME from DEV. Skipping..."
              continue
            fi

            echo "✅ Successfully downloaded $FLOW_NAME revision $LATEST_REV."
          done

      - name: 📤 Upload Shared Flows as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: sharedflows-artifact
          path: sharedflows/
          retention-days: 360

  # Approval Job
  # Job 2: Approval
  QA_APPROVAL:
    name: 🛂  Approve QA Deployment
    runs-on: ubuntu-latest
    needs: [ download-from-dev ]
    environment: "QA_Approval"
    steps:
      - name: Wait for environment approval
        run: echo "No-op step to satisfy GitHub Actions requirement."

  #Step 2: Deploy Same Revision to QA
  deploy-to-qa:
    name: 🚀 Deploy Same Shared Flow Revision to QA
    needs: QA_APPROVAL
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (QA)
        id: auth_qa
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_NONPROD }}

      - name: 🚀 Deploy Same Revision to QA (If Different)
        run: |
          IFS=',' read -ra SHARED_FLOWS <<< "${{ inputs.sharedflows }}"
          for FLOW_NAME in "${SHARED_FLOWS[@]}"; do
            echo "🚀 Checking and deploying $FLOW_NAME from DEV to QA..."

            # Get the active deployed revision in DEV
            DEV_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/dev/sharedflows/$FLOW_NAME/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            if [ -z "$DEV_REV" ] || [ "$DEV_REV" == "null" ]; then
              echo "❌ No active deployed revision found for $FLOW_NAME in DEV. Skipping..."
              continue
            fi

            echo "✅ DEV Active Revision for $FLOW_NAME: $DEV_REV"

            # Get the active deployed revision in QA
            QA_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/qa/sharedflows/$FLOW_NAME/deployments" | \
              jq -r '.deployments[].revision' | sort -nr | head -1)

            echo "✅ QA Active Revision for $FLOW_NAME: ${QA_REV:-None}"

            # Compare revisions and deploy only if different
            if [ "$DEV_REV" == "$QA_REV" ]; then
              echo "⚠️ $FLOW_NAME already has revision $DEV_REV in QA. Skipping deployment."
              continue
            fi

            #  Deploy the same DEV revision to QA
            echo "🚀 Deploying $FLOW_NAME revision $DEV_REV to QA..."
            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth_qa.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_DEV }}/environments/qa/sharedflows/$FLOW_NAME/revisions/$DEV_REV/deployments?override=true"

            echo "✅ Successfully deployed $FLOW_NAME revision $DEV_REV to QA."
          done

  # Job 6: Stage Deployment Approval
  stage_deploy_approval:
    name: 🛂 Approve Stage Deployment
    runs-on: ubuntu-latest
    needs: [ deploy-to-qa ]
    environment: "STAGE_Deploy_Approval"
    steps:
      - name: Wait for environment approval
        run: echo "Wait for APPROVAL"

  # Step 3: Deploy to Stage (New Revision)
  deploy-to-stage:
    name: 🚀 Deploy Shared Flows to Stage
    needs: stage_deploy_approval
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (Stage)
        id: auth_stage
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_STAGE }}

      - name: 📥 Download Shared Flow Artifact
        uses: actions/download-artifact@v4
        with:
          name: sharedflows-artifact
          path: sharedflows/

      - name: 🚀 Upload and Deploy to Stage
        run: |
          for ZIP_FILE in sharedflows/*.zip; do
            FLOW_NAME=$(basename "$ZIP_FILE" .zip)
            echo "🚀 Uploading $FLOW_NAME to Stage..."

            # Upload to Stage (New Revision)
            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
              -F "file=@$ZIP_FILE" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/sharedflows?action=import&name=$FLOW_NAME"

            # Deploy in Stage
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/sharedflows/$FLOW_NAME/revisions" | jq -r 'max')

            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth_stage.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_STAGE }}/environments/stage/sharedflows/$FLOW_NAME/revisions/$LATEST_REV/deployments?override=true"

            echo "✅ Successfully deployed $FLOW_NAME revision $LATEST_REV to Stage."
          done

  # job 8: Prod Deployment Approval
  PROD_APPROVAL:
    name: 🛂 Stage Approval
    runs-on: ubuntu-latest
    needs: [ deploy-to-stage ]
    environment: "PROD_Approval"
    steps:
      - name: Wait for environment approval
        run: echo "APPROVE to proceed to Prod deployment."

  # job 9: Prod Approval
  PROD_DEPLOYMENT_APPROVAL:
    name: 🛂 Approve Prod Deployment
    runs-on: ubuntu-latest
    needs: [ PROD_APPROVAL ]
    environment: "PROD_Deploy_Approval"
    steps:
      - name: Wait for environment approval
        run: echo "APPROVE to proceed to Prod deployment."

  # Job 10: Deploy to Prod
  deploy-to-prod:
    name: 🚀 Deploy Shared Flows to Prod
    needs: PROD_DEPLOYMENT_APPROVAL
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write

    steps:
      - name: 🔑 Authenticate with GCP (Prod)
        id: auth_prod
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ vars.APIGEEX_CICD_SA_PROD }}

      - name: 📥 Download Shared Flow Artifact
        uses: actions/download-artifact@v4
        with:
          name: sharedflows-artifact
          path: sharedflows/

      - name: 🚀 Upload and Deploy to Prod
        run: |
          for ZIP_FILE in sharedflows/*.zip; do
            FLOW_NAME=$(basename "$ZIP_FILE" .zip)
            echo "🚀 Uploading $FLOW_NAME to Prod..."

            # Upload to Prod (New Revision)
            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              -F "file=@$ZIP_FILE" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/sharedflows?action=import&name=$FLOW_NAME"

            # Deploy in Prod
            LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/sharedflows/$FLOW_NAME/revisions" | jq -r 'max')

            curl -X POST \
              -H "Authorization: Bearer ${{ steps.auth_prod.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ vars.APIGEEX_ORG_PROD }}/environments/prod/sharedflows/$FLOW_NAME/revisions/$LATEST_REV/deployments?override=true"

            echo "✅ Successfully deployed $FLOW_NAME revision $LATEST_REV to Prod."
          done
