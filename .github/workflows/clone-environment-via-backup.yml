name: <PERSON><PERSON> Via Backup configs Artifact
run-name: Cloning ENV - ${{ inputs.from_env }} to ${{ inputs.to_env }} via backup ID - ${{ inputs.from_runID }}
on:
  workflow_dispatch:
    inputs:
      from_runID:
        description: "Run ID"
        required: true
      from_env:
        description: "Source Environment"
        required: true
      to_org:
        description: "Target Apigee Organization ID"
        required: false
      to_env:
        description: "Clone to Environment"
        required: true
      to_sa:
        description: "Target Service Account Email"
        required: false

jobs:
 clone-env:
   runs-on: ubuntu-latest
   permissions:
     contents: write
     id-token: write
     actions: read
   steps:
    - name: Authenticate with GCP (Target)
      id: auth_target
      uses: google-github-actions/auth@v2
      with:
        token_format: "access_token"
        create_credentials_file: true
        workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
        service_account: ${{ inputs.to_sa }}
        
    - name: download artifact
      uses: actions/download-artifact@v4
      with:
        name: apigee-config-backup
        github-token: ${{ secrets.GITHUB_TOKEN }}
        repository: cox-cei/apigeex-CICD-Central
        run-id: ${{ inputs.from_runID }}
       
    - name: list the files
      run: |
         ls -lr

 deploy-approval:
    name: Approval to deploy on ${{ inputs.to_env }}
    needs: [ clone-env ]
    runs-on: ubuntu-latest
    environment: ${{ inputs.to_env }}
    steps:
      - name: Clone Approval
        run: |
          echo "Deploy Approval"

 deploy-configs:
    name: Deploy in ${{ inputs.to_org }} (${{ inputs.to_env }})
    needs: [ deploy-approval ]
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
      actions: read
    steps:
      - name: Authenticate with GCP (Target)
        id: auth_target
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ vars.APIGEEX_CICD_WIF }}
          service_account: ${{ inputs.to_sa }}

      - name: Download Stored Artifacts
        uses: actions/download-artifact@v4
        with:
            name: apigee-config-backup
            github-token: ${{ secrets.GITHUB_TOKEN }}
            repository: cox-cei/apigeex-CICD-Central
            run-id: ${{ inputs.from_runID }}
            
      - name: List Files
        run : |
          ls -l
          tree -d

      # - name: Deploy Target Servers
      #   run: |
      #     for FILE in target_servers/*.json; do
      #       SERVER_NAME=$(basename "$FILE" .json)
      #       echo "Deploying Target Server: $SERVER_NAME"

      #       curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         -H "Content-Type: application/json" -d "@$FILE" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/targetservers"
      #     done

      # # Deploy KVMs to target
      # - name: Deploy KVMs
      #   run: |
      #     for FILE in kvms/*.json; do
      #       KVM_NAME=$(basename "$FILE" .json)

      #       # Skip processing entry files
      #       if [[ "$KVM_NAME" == *"-entries" ]]; then
      #         echo "Skipping KVM entry file: $KVM_NAME"
      #         continue
      #       fi

      #       ENTRIES_FILE="kvms/${KVM_NAME}-entries.json"
      #       echo "Processing KVM: $KVM_NAME"

      #       # Check KVM existence using direct HTTP status code
      #       STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" -X GET \
      #         -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps/$KVM_NAME")

      #       # Create KVM if it does not exist
      #       if [[ "$STATUS_CODE" -ne 200 ]]; then
      #         echo "Creating new KVM: $KVM_NAME"

      #         CREATE_RESPONSE=$(curl -s -o create_response.json -w "%{http_code}" -X POST \
      #           -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #           -H "Content-Type: application/json" \
      #           -d "{\"name\": \"$KVM_NAME\"}" \
      #           "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps")

      #         if [[ "$CREATE_RESPONSE" -ne 201 ]]; then
      #           echo "Failed to create KVM: $KVM_NAME (HTTP $CREATE_RESPONSE)"
      #           cat create_response.json
      #           exit 1
      #         fi
      #       else
      #         echo "KVM $KVM_NAME already exists. Proceeding with entry updates."
      #       fi

      #       # Process entries if the entries file exists
      #       if [[ -f "$ENTRIES_FILE" ]]; then
      #         echo "Processing entries for KVM: $KVM_NAME"

      #         # Extract valid key-value pairs
      #         jq -c '.keyValueEntries[]' "$ENTRIES_FILE" | while read -r ENTRY; do
      #           ENTRY_NAME=$(echo "$ENTRY" | jq -r '.name')
      #           ENTRY_VALUE=$(echo "$ENTRY" | jq -r '.value | @json')

      #           echo "Adding entry: $ENTRY_NAME"
      #           ENTRY_RESPONSE=$(curl -s -o entry_response.json -w "%{http_code}" -X POST \
      #             -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #             -H "Content-Type: application/json" \
      #             -d "{\"name\": \"$ENTRY_NAME\", \"value\": $ENTRY_VALUE}" \
      #             "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/keyvaluemaps/$KVM_NAME/entries")

      #           # Allow HTTP 201 (Created) as a success response
      #           if [[ "$ENTRY_RESPONSE" -ne 200 && "$ENTRY_RESPONSE" -ne 201 ]]; then
      #             echo "Failed to add entry $ENTRY_NAME (HTTP $ENTRY_RESPONSE)"
      #             cat entry_response.json
      #             exit 1
      #           fi
      #         done
      #       else
      #         echo "No entries file found for $KVM_NAME"
      #       fi
      #     done

      # - name: Deploy Shared Flows
      #   run: |
      #     for FILE in shared_flows/*.zip; do
      #       FLOW_NAME=$(basename "$FILE" .zip)
      #       echo "Deploying Shared Flow: $FLOW_NAME"

      #       curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         -F "file=@$FILE" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/sharedflows?action=import&name=$FLOW_NAME"

      #       # Get latest revision number
      #       LATEST_REV=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/sharedflows/$FLOW_NAME/revisions" | \
      #         jq -r '.[-1]')

      #       echo "Deploying Shared Flow: $FLOW_NAME Revision: $LATEST_REV"
      #       curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/sharedflows/$FLOW_NAME/revisions/$LATEST_REV/deployments?override=true"
      #     done

      # - name: Deploy Proxies from Backup to QA (Skip Existing)
      #   run: |
      #     for FILE in apis/*.zip; do
      #       PROXY_NAME=$(basename "$FILE" .zip)
      #       echo "Cxhecking if proxy $PROXY_NAME already exists in Apigee..."

      #       STATUS_CODE=$(curl -s -o /dev/null -w "%{http_code}" \
      #         -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apis/$PROXY_NAME")

      #       if [[ "$STATUS_CODE" -eq 200 ]]; then
      #         echo "Proxy $PROXY_NAME already exists. Skipping deployment."
      #         continue
      #       fi

      #       echo "Importing and deploying proxy: $PROXY_NAME"
      #       RESPONSE=$(curl -s -o response.json -w "%{http_code}" -X POST \
      #         -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         -F "file=@$FILE" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apis?action=import&name=$PROXY_NAME")

      #       if [[ "$RESPONSE" -ne 200 && "$RESPONSE" -ne 201 ]]; then
      #         echo "Failed to import proxy $PROXY_NAME (HTTP $RESPONSE)"
      #         cat response.json
      #         exit 1
      #       fi

      #       LATEST_REV=$(jq -r '.revision' response.json)

      #       echo "Deploying revision $LATEST_REV of $PROXY_NAME to ${{ inputs.to_env }}"
      #       curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
      #         "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/environments/${{ inputs.to_env }}/apis/$PROXY_NAME/revisions/$LATEST_REV/deployments?override=true"
      #     done

    #   - name: Deploy API Products
    #     run: |
    #       for FILE in products/*.json; do
    #         PRODUCT_NAME=$(jq -r '.name' "$FILE")
    #         echo "Checking if API Product exists: $PRODUCT_NAME in ${{ inputs.to_env }}"

    #         RESPONSE=$(curl -s -o response.json -w "%{http_code}" -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
    #           "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts/$PRODUCT_NAME")

    #         HTTP_STATUS=$(cat response.json | jq -r '.error.code')

    #         # Remove metadata fields (createdAt, lastModifiedAt) and update environment dynamically
    #         UPDATED_PAYLOAD=$(jq --arg target_env "${{ inputs.to_env }}" \
    #           'del(.createdAt, .lastModifiedAt) | .environments = [$target_env]' "$FILE")

    #         echo "$UPDATED_PAYLOAD" > updated_product.json

    #         if [[ "$HTTP_STATUS" == "404" ]]; then
    #           echo "Creating API Product: $PRODUCT_NAME"
    #           curl -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
    #             -H "Content-Type: application/json" -d "@updated_product.json" \
    #             "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts"
    #         else
    #           echo "Updating API Product: $PRODUCT_NAME"
    #           curl -X PUT -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
    #             -H "Content-Type: application/json" -d "@updated_product.json" \
    #             "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/apiproducts/$PRODUCT_NAME"
    #         fi
    #       done
      - name: Deploy All Apigee Apps
        run: |
          TARGET_SERVICE_ACCOUNT="${{ inputs.to_sa }}"
            
          echo "Fetching Developer ID for: $TARGET_SERVICE_ACCOUNT"
          TARGET_DEVELOPER_RESPONSE=$(curl -s -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
            "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_SERVICE_ACCOUNT")
            
          TARGET_DEVELOPER_ID=$(echo "$TARGET_DEVELOPER_RESPONSE" | jq -r '.developerId')
            
          if [[ -z "$TARGET_DEVELOPER_ID" || "$TARGET_DEVELOPER_ID" == "null" ]]; then
            echo "Error: Could not fetch Developer ID. Exiting."
            exit 1
          fi
            
          for FILE in apps/*.json; do
            APP_NAME=$(jq -r '.name' "$FILE")
            echo "Processing App: $APP_NAME"
            
            # Check if App exists
            APP_STATUS=$(curl -s -o response.json -w "%{http_code}" -X GET \
              -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME")
            
            if [[ "$APP_STATUS" -ne 200 ]]; then
              # Create app (without credentials)
              APP_PAYLOAD=$(jq --arg devId "$TARGET_DEVELOPER_ID" \
                'del(.createdAt, .lastModifiedAt, .credentials, .developerId) | .developerId = $devId' "$FILE")
            
              CREATE_RESPONSE=$(curl -s -o create_response.json -w "%{http_code}" -X POST \
                -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "$APP_PAYLOAD" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps")
            
              if [[ "$CREATE_RESPONSE" -ne 201 ]]; then
                echo "Failed to create App $APP_NAME"
                cat create_response.json
                exit 1
              fi
            else
              echo "App $APP_NAME already exists. Skipping creation."
            fi
            
            # Extract key, secret, products
            CONSUMER_KEY=$(jq -r '.credentials[0].consumerKey' "$FILE")
            CONSUMER_SECRET=$(jq -r '.credentials[0].consumerSecret' "$FILE")
            PRODUCTS=$(jq -r '.credentials[0].apiProducts[].apiproduct' "$FILE" | jq -R -s -c 'split("\n")[:-1]')
            
            # Step 1: Create key with secret (no product attached here)
            KEY_PAYLOAD=$(jq -n \
              --arg key "$CONSUMER_KEY" \
              --arg secret "$CONSUMER_SECRET" \
              '{consumerKey: $key, consumerSecret: $secret}')
            
            echo "Creating credential for App: $APP_NAME"
            curl -s -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
              -H "Content-Type: application/json" -d "$KEY_PAYLOAD" \
              "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME/keys/create"
            
            # Step 2: Associate API Products to the created key
            if [[ ! -z "$PRODUCTS" && "$PRODUCTS" != "[]" ]]; then
              PRODUCT_PAYLOAD=$(jq -n --argjson products "$PRODUCTS" '{"apiProducts": $products}')
              echo "Associating products to key: $CONSUMER_KEY for app: $APP_NAME"
            
              curl -s -X POST -H "Authorization: Bearer ${{ steps.auth_target.outputs.access_token }}" \
                -H "Content-Type: application/json" -d "$PRODUCT_PAYLOAD" \
                "https://apigee.googleapis.com/v1/organizations/${{ inputs.to_org }}/developers/$TARGET_DEVELOPER_ID/apps/$APP_NAME/keys/$CONSUMER_KEY"
            else
              echo "No products to associate for $APP_NAME"
            fi
          done
  
    