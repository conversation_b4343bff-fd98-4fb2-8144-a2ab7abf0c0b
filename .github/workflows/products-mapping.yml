name: 🧩 Product Validate & Fix Mapping
run-name: 🧩 Valide products on apigee ${{ inputs.environment }}
on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Apigee environment (dev, qa, stage, prod)'
        required: true
      apigee_org:
        description: 'Apigee Organization ID'
        required: true

jobs:
  validate-mapping:
    name: Validate & Fix Product Mapping
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read

    env:
      DEV_STAGE_ORG: cei-tdgkxj26
      STAGE_ORG: cei-lfj1ov9u
      PROD_ORG: cei-vbxef9zh

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Install Dependencies
        run: |
          sudo apt-get update && sudo apt-get install -y jq python3 python3-pip
          pip3 install requests pandas openpyxl

      - name: Set Org and Service Account
        id: envmap
        run: |
          case "${{ inputs.environment }}" in
            dev|qa)
              echo "org=${{ env.DEV_STAGE_ORG }}" >> $GITHUB_OUTPUT
              echo "sa=${{ vars.APIGEEX_CICD_SA_NONPROD }}" >> $GITHUB_OUTPUT
              echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
              ;;
            stage)
              echo "org=${{ env.STAGE_ORG }}" >> $GITHUB_OUTPUT
              echo "sa=${{ vars.APIGEEX_CICD_SA_STAGE }}" >> $GITHUB_OUTPUT
              echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
              ;;
            prod)
              echo "org=${{ env.PROD_ORG }}" >> $GITHUB_OUTPUT
              echo "sa=${{ vars.APIGEEX_CICD_SA_PROD }}" >> $GITHUB_OUTPUT
              echo "wif=${{ vars.APIGEEX_CICD_WIF }}" >> $GITHUB_OUTPUT
              ;;
          esac

      - name: Authenticate with GCP
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          create_credentials_file: true
          workload_identity_provider: ${{ steps.envmap.outputs.wif }}
          service_account: ${{ steps.envmap.outputs.sa }}

      - name: Convert Excel to JSON
        run: |
          python3 .github/scripts/excel-json-mapping.py \
            --excel_file Apigee_Apps_Summary.xlsx \
            --output_file product-mappings/temp_products.json
            
      - name: View Generated Product Mapping
        run: |
          echo "Generated Mapping File:"
          cat product-mappings/temp_products.json
            
      - name: List All Files
        run: |
          echo "🔍 Listing working directory:"
          ls -R
          
      - name: Validate and Fix Product-Proxies
        run: |
          python3 .github/scripts/validate_product_mappings.py \
            --input_json_file product-mappings/temp_products.json \
            --apigee_org "${{ steps.envmap.outputs.org }}" \
            --environment "${{ inputs.environment }}" \
            --access_token "${{ steps.auth.outputs.access_token }}"
