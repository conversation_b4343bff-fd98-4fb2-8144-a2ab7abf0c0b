# 🚀 Apigee CI/CD Pipeline

## Overview
The **Apigee CI/CD Pipeline** automates the deployment of Apigee proxies, API products, and related configurations across **DEV, QA, Stage, and Prod** environments. The pipeline ensures consistency, enforces validation, and triggers approvals at various stages.

## 📌 Core Features
- **Automated Proxy Deployment**: Deploys Apigee proxies to multiple environments (DEV → QA → Stage → Prod).
- **API Product Management**: Ensures API products are correctly associated with proxies.
- **Version Control & Rollbacks**: Tracks revisions and provides rollback support.
- **Approval Workflow**: Requires manual approval before progressing beyond QA and Stage.
- **Artifact Management**: Stores and retrieves deployment artifacts for traceability.
- **Notifications**: Sends email updates on deployment status using **SendGrid**.
- **New Relic Integration**: Posts deployment markers to New Relic for monitoring.

---

## 🔄 Workflow Categories

### 🚀 Deployment Workflows

#### 1. Centralized Deployment Pipeline
**Workflow File**: `.github/workflows/centralized-deployment-CICD.yml`

**Purpose**: Main CI/CD pipeline that automates the deployment of Apigee proxies across all environments.

**Key Features**:
- Deploys proxies through DEV → QA → Stage → Prod progression
- Manages API product associations
- Requires approvals at key stages
- Sends notifications via email
- Creates GitHub releases for version tracking
- Posts deployment markers to New Relic

**Usage Example**:
```yaml
# Triggered via PR or manually with these inputs:
repository: "my-apigee-repo"
branch: "feature/new-api"
commit_sha: "abc123"
commit_author: "username"
pr_title: "Add new API feature"
commit_author_name: "John Doe"
commit_author_email: "<EMAIL>"
pr_number: "42"
artifact_id: "12345"
run_id: "67890"
```

#### 2. Single Proxy Deployment
**Workflow File**: `.github/workflows/single-proxy-depoly.yml`

**Purpose**: Deploys a single specified proxy across environments.

**Key Features**:
- Targets a specific proxy for deployment
- Follows the same environment progression
- Useful for quick updates to individual proxies

**Usage Example**:
```yaml
# Triggered manually with:
proxy_name: "CCI-sc-wireless-example"
```

#### 3. Shared Flow Deployment
**Workflow File**: `.github/workflows/deploy-shared-flows.yml`

**Purpose**: Deploys shared flows across environments.

**Key Features**:
- Handles multiple shared flows in one operation
- Maintains consistent shared flow versions across environments

**Usage Example**:
```yaml
# Triggered manually with:
sharedflows: "SF-Logging,SF-ErrorHandler"
```

#### 4. Target Servers Deployment
**Workflow File**: `.github/workflows/target-servers-deploy.yml`

**Purpose**: Manages target server configurations across environments.

**Key Features**:
- Deploys target server configurations
- Supports multiple environments

**Usage Example**:
```yaml
# Triggered manually with environment selection
```

---

### 🔧 Management Workflows

#### 1. API Product Management
**Workflow File**: `.github/workflows/products-mapping.yml`

**Purpose**: Validates and fixes API product mappings.

**Key Features**:
- Converts Excel mappings to JSON
- Validates product configurations
- Fixes inconsistencies in product mappings

**Usage Example**:
```yaml
# Triggered manually with:
environment: "dev"
apigee_org: "cei-tdgkxj26"
```

#### 2. App & Product Validation
**Workflow File**: `.github/workflows/validate-app-products-mapping.yml`

**Purpose**: Validates and fixes Apigee app and product associations.

**Key Features**:
- Ensures apps have correct product access
- Fixes misconfigurations automatically

**Usage Example**:
```yaml
# Triggered manually with:
environment: "dev"
```

#### 3. App Credential Management
**Workflow File**: `.github/workflows/app-password-generator.yml`

**Purpose**: Creates new credentials for Apigee apps.

**Key Features**:
- Generates new API keys and secrets
- Option to delete old credentials
- Requires approval for production changes

**Usage Example**:
```yaml
# Triggered manually with:
environment: "dev"
app_names: "App1,App2,App3"
delete_old_keys: "yes"
```

#### 4. KVM Management
**Workflow File**: `.github/workflows/kvm-workflow.yml`

**Purpose**: Manages Key-Value Maps (KVMs) in Apigee.

**Key Features**:
- Create, update, delete, or view KVMs
- Supports proxy-specific and environment-level KVMs
- Handles multiple keys and values

**Usage Example**:
```yaml
# Triggered manually with:
proxies: "MyProxy1,MyProxy2"  # Optional
kvm_action: "create"
kvm_name: "ConfigKVM"
environment: "dev"
kvm_keys: "key1,key2"
kvm_values: "value1,value2"
```

---

### 🔄 Environment Management Workflows

#### 1. Environment Replication
**Workflow File**: `.github/workflows/replicate-apigee-environment.yml`

**Purpose**: Replicates configurations from one environment to another.

**Key Features**:
- Copies proxies, products, and configurations
- Supports cross-organization replication
- Requires approval for sensitive environments

**Usage Example**:
```yaml
# Triggered manually with source and target environment details
```

#### 2. Environment Cloning via Backup
**Workflow File**: `.github/workflows/clone-environment-via-backup.yml`

**Purpose**: Clones an environment using a previous backup.

**Key Features**:
- Restores from a specific run ID
- Supports cross-organization cloning
- Requires approval for sensitive environments

**Usage Example**:
```yaml
# Triggered manually with backup details and target environment
```

---

### 🛠️ Utility Workflows

#### 1. Failover KVM Switch
**Workflow File**: `.github/workflows/kvm_switch.yml`

**Purpose**: Manages failover between primary and secondary systems.

**Key Features**:
- Switches between primary and secondary configurations
- Updates KVMs to reflect the current active system

**Usage Example**:
```yaml
# Triggered manually with:
environment: "prod"
failover_state: "secondary"
```

#### 2. Connection Testing
**Workflow File**: `.github/workflows/connection-test.yml`

**Purpose**: Tests connectivity to Apigee organizations.

**Key Features**:
- Verifies authentication and access
- Validates environment configurations

**Usage Example**:
```yaml
# Triggered manually with environment selection
```

#### 3. Rollback Deployment
**Workflow File**: `.github/workflows/rollback-deployment.yml`

**Purpose**: Rolls back to a previous deployment state.

**Key Features**:
- Reverts to a specific release tag
- Can target specific proxies or all proxies
- Sends rollback notifications

**Usage Example**:
```yaml
# Triggered manually with:
release_tag: "release-v1.2.3"
environment: "prod"
proxy_name: "MyProxy"  # Optional, leave blank for all proxies
```

---

## 🎯 Workflow Execution
### **Triggering the Workflow**
The main deployment workflow can be triggered in the following ways:
1. **Pull Request (PR) Creation**: Runs automatically when a PR is created targeting the `dev` or `main` branch.
2. **Manual Trigger**: Can be executed via `workflow_dispatch` by providing required inputs.

### **Workflow Inputs**
| Input Name           | Description                                      | Required |
|---------------------|--------------------------------------------------|----------|
| `repository`        | Name of the triggering repository               | ✅        |
| `branch`           | Branch that triggered the workflow               | ✅        |
| `commit_sha`       | Commit SHA for reference                         | ✅        |
| `commit_author`    | Author of the commit                            | ✅        |
| `pr_title`         | Title of the PR triggering the workflow          | ✅        |
| `commit_author_name` | Name of the commit author                      | ✅        |
| `commit_author_email`| Email of the commit author                     | ✅        |
| `pr_number`        | Number of the PR that triggered this run        | ✅        |
| `artifact_id`      | Proxies artifact ID for retrieval                | ✅        |
| `run_id`           | Unique run ID for tracking                      | ✅        |

---

## 🔄 Pipeline Stages

### **1️⃣ Deploy Proxies to DEV**
- Fetches the latest proxy artifacts.
- Extracts and validates proxies.
- Deploys proxies to the DEV environment.
- Generates a **deployment revision mapping** for reference.
- Notifies stakeholders via **email**.
- Uploads artifacts for further stages.

### **2️⃣ Deploy Proxies to QA**
- Downloads **DEV deployment revisions**.
- Ensures QA gets the latest revisions from DEV.
- Deploys proxies to QA.
- Updates **API Product Mapping**.
- Notifies stakeholders and uploads artifacts.
- Requires **manual approval** to proceed to Stage.

### **3️⃣ Deploy Proxies to Stage**
- Waits for **Stage approval**.
- Uses the latest **QA release tag** to fetch correct proxy versions.
- Deploys proxies to Stage.
- Ensures correct API product associations.
- Notifies stakeholders.
- Uploads **Stage deployment revisions**.
- Requires **manual approval** to proceed to Prod.

### **4️⃣ Deploy Proxies to PROD**
- Waits for **Prod approval**.
- Uses the latest **Stage deployment mapping** to fetch correct proxy versions.
- Deploys proxies to PROD.
- Ensures API products are properly configured.
- Notifies stakeholders.

### **5️⃣ Release & Merge PRs**
- Stores **final deployment revisions** in a GitHub Release.
- Auto-approves and merges the **feature → dev** PR.
- Creates a **dev → main** PR automatically.

---

## 📥 Artifact Management
The pipeline uses **GitHub Artifacts** to store deployment details at each stage:
- **`dev-proxy-revisions`** → Stores revisions deployed to DEV.
- **`qa-proxy-revisions`** → Stores revisions deployed to QA.
- **`stage-deployment-mapping`** → Stores mapping of QA → Stage.
- **`prod-deployment-mapping`** → Stores mapping of Stage → Prod.
- **`api-product-mapping`** → Stores API product configurations.

---

## 🔔 Notifications & Logging
The pipeline provides detailed notifications:
- **Email Notifications** via **SendGrid** for each deployment stage.
- Deployment summaries include:
  - ✅ Successfully deployed proxies.
  - ⚠️ Skipped proxies (already deployed at latest revision).
  - ❌ Failed deployments (errors logged).
- Logs are available within the GitHub Actions run.
- **New Relic Markers** are posted for monitoring and tracking deployments.

---

## 🔄 Rollback Support
The **rollback workflow** allows rolling back to a previous deployment:
- Can rollback **all proxies** or a **specific proxy**.
- Fetches stored **deployment mapping files** for accuracy.
- Reverts to a **previously deployed revision**.
- Sends a rollback notification via email.

---

## 🛠️ Dependencies & Tools
- **Google Cloud Auth** (`google-github-actions/auth@v2`) for Apigee authentication.
- **Apigee Management APIs** for deploying and managing API products.
- **SendGrid API** for email notifications.
- **GitHub Artifacts** for storing and retrieving deployment data.
- **New Relic API** for posting deployment markers.
- **Python Scripts** for processing Excel files and managing deployments.

---

## 📌 Workflow Interactions
- **Centralized Deployment** → Creates artifacts used by **Rollback Workflow**
- **Environment Replication** → Uses artifacts from previous deployments
- **Product Mapping** → Supports the **Centralized Deployment** workflow
- **KVM Management** → Can be used before or after deployments to configure environment variables

---

## 📌 Future Enhancements
🔹 Improve rollback granularity for partial rollbacks.
🔹 Enhance validation for API products and proxies.
🔹 Implement dynamic retry mechanisms for failed deployments.
🔹 Add detailed release notes automation.
🔹 Expand New Relic integration for better monitoring.
🔹 Add support for Apigee X extensions and add-ons.

---

## 🚀 Conclusion
This **Apigee CI/CD pipeline** automates API proxy and product deployments efficiently across environments while enforcing governance, approvals, and rollback capabilities. The workflow ensures **seamless integration**, **traceability**, and **automated governance** for all Apigee deployments.

For any modifications or enhancements, update the pipeline workflows accordingly in `.github/workflows/`.

---

🎯 **Maintainer:** DevOps Team
📧 **Support Contact:** <<EMAIL>>
